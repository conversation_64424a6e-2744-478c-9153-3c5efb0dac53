extends Node

@export var port: int = 9999
@export var max_clients: int = 32
@export var host: String = "**************"
@export var use_localhost_in_editor: bool = true
#"*************"
signal connected
signal disconnected
signal peer_connected(id:int)
signal peer_disconnected(id:int)



static var is_peer_connected: bool = false

func _ready():
	# Connect signals to update the static variable
	connected.connect(func(): MultiplayerController.is_peer_connected = true)
	disconnected.connect(func(): MultiplayerController.is_peer_connected = false)

# --- Public Functions (called by UI or other scripts) ---
func start_server():

	var peer = ENetMultiplayerPeer.new()
	var err = peer.create_server(Global.port, max_clients)
	#ar err = peer.create_server(Global.port, max_clients)
	if err != OK:
		print("SERVER ERROR: Cannot start server. Err: " + str(err))
		disconnected.emit()
		return
	
	multiplayer.multiplayer_peer = peer
	multiplayer.peer_connected.connect(_on_peer_connected)
	multiplayer.peer_disconnected.connect(_on_peer_disconnected)
	print("Server started successfully on port %d" % Global.port)
	connected.emit()

func start_client():
	
	#var address = host
	##if OS.has_feature("editor") and use_localhost_in_editor:
		##address = "localhost"
	
	var peer = ENetMultiplayerPeer.new()
	var err = peer.create_client(host, Global.port)
	if err != OK:
		print("CLIENT ERROR: Cannot start client. Err: " + str(err))
		disconnected.emit()
		return
	
	multiplayer.multiplayer_peer = peer
	multiplayer.connected_to_server.connect(_on_connected_to_server)
	multiplayer.server_disconnected.connect(_on_server_connection_failure)
	multiplayer.connection_failed.connect(_on_server_connection_failure)
	#print("Attempting to connect to %s:%d" % [address, port])

# --- Internal Signal Handlers ---
func _on_connected_to_server():
	print("Successfully connected to server.")
	connected.emit()

func _on_server_connection_failure():
	print("Connection failed or lost.")
	multiplayer.multiplayer_peer = null
	disconnected.emit()

func _on_peer_connected(id: int):
	print("Controller: New peer connected with ID: " + str(id))
	# Find the main game node and tell it a player has joined.
	var game_node = get_tree().get_root().get_node_or_null("Game")
	peer_connected.emit(id)
	
	
	if is_instance_valid(game_node):
		game_node.peer_connected.rpc(id)
	else:
		print("CONTROLLER ERROR: Could not find the 'Game' node!")

func _on_peer_disconnected(id: int):
	print("Controller: Peer with ID %d has disconnected." % id)
	# Find the main game node and tell it a player has left.
	var game_node = get_tree().get_root().get_node_or_null("Game")
	peer_disconnected.emit(id)
	
	if is_instance_valid(game_node):
		game_node.peer_disconnected.rpc(id)
