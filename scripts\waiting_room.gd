extends Node3D


#@onready var character: MeshInstance3D = $Player/RootNode/CharacterArmature/Skeleton3D/Character
var local_message_hud = preload("res://send_local_message.tscn")
@onready var global_chat_panel: Panel = %GlobalChatPanel
@onready var message_list: VBoxContainer = %MessageList


func _ready():
	pass
	#TEST
	if Global.server:
		MultiplayerController.start_server()
	else:
		MultiplayerController.start_client()
	#if multiplayer.is_server():
		#print("hello")

# Function to clear the text when the timer times out
func _on_delete_text_timer_timeout(timer: Timer) -> void:
	#message.text = ""
	timer.queue_free()


func _on_local_chat_pressed() -> void:
	var message = local_message_hud.instantiate()
	message.connect("send_message",_on_message_sent)
	add_child(message)
	#panel.visible = !panel.visible
	
func _on_message_sent(message:String):
	# Check if message is not empty
	if message.strip_edges().length() == 0:
		print("Warning: Empty message not sent")
		return

	print("Local message sent: " + message)

	# Find the local player (the one with authority)
	var players_node = get_node_or_null("Players")
	if players_node:
		for child in players_node.get_children():
			if child.is_multiplayer_authority():
				# Call the send_local_message RPC on the local player
				child.send_local_message.rpc(message)
				break
	else:
		print("Warning: Could not find Players node")


func _on_global_chat_pressed() -> void:
	# If already visible, hide it again with a reverse animation
	var tween = create_tween().set_trans(Tween.TRANS_SINE).set_ease(Tween.EASE_OUT)
	var target_x

	if not global_chat_panel.visible:
		global_chat_panel.visible = true

		# Start from off-screen right
		global_chat_panel.position.x = get_viewport().size.x + 50  

		# Bounce into place (a little past and then back)
		target_x = get_viewport().size.x - global_chat_panel.size.x

		tween.tween_property(global_chat_panel, "position:x", target_x - 20, 0.35)  # overshoot
		tween.tween_property(global_chat_panel, "position:x", target_x, 0.15)      # bounce back

	else:
		# Slide out smoothly when hiding
		target_x = get_viewport().size.x + 50
		tween.tween_property(global_chat_panel, "position:x", target_x, 0.3)
		await tween.finished
		global_chat_panel.visible = false

	


func _on_send_global_message_pressed() -> void:
	# Get Sender ID
	button_bounce(%SendGlobal_message)
	var message  = %GlobalTextField.text
	var player_name: String
	print(message)
	var player_node = get_node_or_null("%Players")
	for child in player_node.get_children():
		if child.is_multiplayer_authority():
			player_name = child.name
			print(player_name)
			
	
	var msg := Label.new()
	msg.text =  player_name + ": " + message
	message_list.add_child(msg)
	%GlobalTextField.clear()
	

func button_bounce(btn: Button):
	btn.set_pivot_offset(btn.size / 2)
	var original_scale = btn.scale

	# Cancel any previous tween to avoid stacking
	btn.remove_meta("bounce_tween")
	
	var tween = create_tween()
	tween.set_trans(Tween.TRANS_BACK).set_ease(Tween.EASE_OUT)

	# Press-down: shrink fast
	tween.tween_property(btn, "scale", original_scale * 0.9, 0.08)

	# Bounce back up: overshoot slightly for a gamey feel
	tween.tween_property(btn, "scale", original_scale * 1.05, 0.12)
	tween.tween_property(btn, "scale", original_scale, 0.1)

	btn.set_meta("bounce_tween", tween)
