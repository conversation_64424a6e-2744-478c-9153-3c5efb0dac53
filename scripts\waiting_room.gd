extends Node3D


#@onready var character: MeshInstance3D = $Player/RootNode/CharacterArmature/Skeleton3D/Character
var local_message_hud = preload("res://send_local_message.tscn")
@onready var global_chat_panel: Panel = %GlobalChatPanel
@onready var message_list: VBoxContainer = %MessageList


func _ready():
	# Connect the text field to handle Enter key press
	var global_text_field = %GlobalTextField
	if global_text_field:
		global_text_field.text_submitted.connect(_on_global_text_field_submitted)

	#TEST
	if Global.server:
		MultiplayerController.start_server()
	else:
		MultiplayerController.start_client()
	#if multiplayer.is_server():
		#print("hello")

# Function to clear the text when the timer times out
func _on_delete_text_timer_timeout(timer: Timer) -> void:
	#message.text = ""
	timer.queue_free()


func _on_local_chat_pressed() -> void:
	var message = local_message_hud.instantiate()
	message.connect("send_message",_on_message_sent)
	add_child(message)
	#panel.visible = !panel.visible
	
func _on_message_sent(message:String):
	# Check if message is not empty
	if message.strip_edges().length() == 0:
		print("Warning: Empty message not sent")
		return

	print("Local message sent: " + message)

	# Find the local player (the one with authority)
	var players_node = get_node_or_null("Players")
	if players_node:
		for child in players_node.get_children():
			if child.is_multiplayer_authority():
				# Call the send_local_message RPC on the local player
				child.send_local_message.rpc(message)
				break
	else:
		print("Warning: Could not find Players node")


func _on_global_chat_pressed() -> void:
	# If already visible, hide it again with a reverse animation
	var tween = create_tween().set_trans(Tween.TRANS_SINE).set_ease(Tween.EASE_OUT)
	var target_x

	if not global_chat_panel.visible:
		global_chat_panel.visible = true

		# Start from off-screen right
		global_chat_panel.position.x = get_viewport().size.x + 50  

		# Bounce into place (a little past and then back)
		target_x = get_viewport().size.x - global_chat_panel.size.x

		tween.tween_property(global_chat_panel, "position:x", target_x - 20, 0.35)  # overshoot
		tween.tween_property(global_chat_panel, "position:x", target_x, 0.15)      # bounce back

	else:
		# Slide out smoothly when hiding
		target_x = get_viewport().size.x + 50
		tween.tween_property(global_chat_panel, "position:x", target_x, 0.3)
		await tween.finished
		global_chat_panel.visible = false

	


func _on_send_global_message_pressed() -> void:
	# Get Sender ID
	button_bounce(%SendGlobal_message)
	var message = %GlobalTextField.text.strip_edges()

	# Check if message is not empty
	if message.length() == 0:
		print("Warning: Empty global message not sent")
		return

	var player_name = get_player_display_name()
	print("Sending global message from %s: %s" % [player_name, message])

	# Send the global message via RPC to all players
	send_global_chat_message.rpc(player_name, message)
	%GlobalTextField.clear()


# Handle Enter key press in the text field
func _on_global_text_field_submitted(_text: String):
	_on_send_global_message_pressed()


# Get a better player name (try to use Global.player_name or fallback to multiplayer ID)
func get_player_display_name() -> String:
	# Try to get a proper player name from Global if available
	if Global.has_method("get_player_name") and Global.get_player_name() != "":
		return Global.get_player_name()
	elif Global.has_property("player_name") and Global.player_name != "":
		return Global.player_name
	elif Global.has_property("global_player_id"):
		return "Player " + str(Global.global_player_id)
	else:
		# Fallback to multiplayer ID
		var player_node = get_node_or_null("%Players")
		if player_node:
			for child in player_node.get_children():
				if child.is_multiplayer_authority():
					return "Player " + child.name
		return "Unknown Player"


# RPC function to send global chat message to all players
@rpc("any_peer", "call_local", "reliable")
func send_global_chat_message(sender_name: String, message: String):
	print("Received global message from %s: %s" % [sender_name, message])
	add_message_to_global_chat(sender_name, message)


# Function to add message to the global chat UI
func add_message_to_global_chat(sender_name: String, message: String):
	var msg := RichTextLabel.new()

	# Get current time for timestamp
	var time = Time.get_datetime_dict_from_system()
	var timestamp = "[%02d:%02d] " % [time.hour, time.minute]

	# Create colored message with timestamp
	var is_local_player = (sender_name == get_player_display_name())
	var color = "#00ff00" if is_local_player else "#ffffff"  # Green for local player, white for others

	msg.bbcode_enabled = true
	msg.text = "[color=#888888]%s[/color][color=%s]%s:[/color] %s" % [timestamp, color, sender_name, message]
	msg.fit_content = true
	msg.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	msg.custom_minimum_size.x = message_list.size.x - 20  # Leave some padding
	msg.custom_minimum_size.y = 30  # Minimum height for readability

	message_list.add_child(msg)

	# Limit the number of messages to prevent memory issues
	if message_list.get_child_count() > 50:
		message_list.get_child(0).queue_free()

	# Auto-scroll to bottom to show latest message
	await get_tree().process_frame  # Wait for the UI to update
	if message_list.get_parent() is ScrollContainer:
		var scroll_container = message_list.get_parent() as ScrollContainer
		scroll_container.scroll_vertical = scroll_container.get_v_scroll_bar().max_value


func button_bounce(btn: Button):
	btn.set_pivot_offset(btn.size / 2)
	var original_scale = btn.scale

	# Cancel any previous tween to avoid stacking
	btn.remove_meta("bounce_tween")
	
	var tween = create_tween()
	tween.set_trans(Tween.TRANS_BACK).set_ease(Tween.EASE_OUT)

	# Press-down: shrink fast
	tween.tween_property(btn, "scale", original_scale * 0.9, 0.08)

	# Bounce back up: overshoot slightly for a gamey feel
	tween.tween_property(btn, "scale", original_scale * 1.05, 0.12)
	tween.tween_property(btn, "scale", original_scale, 0.1)

	btn.set_meta("bounce_tween", tween)
