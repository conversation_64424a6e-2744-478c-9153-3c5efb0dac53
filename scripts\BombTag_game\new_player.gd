extends CharacterBody3D
class_name Player

## Camera Settings
@export var mouse_sensitivity := 0.2

## Movement Settings
@export var speed := 10.0
@export var acceleration := 20.0
@export var rotation_speed := 12.0
@export var jump_impulse := 15.0
@export var respawn_position: Vector3 = Vector3(12.326,7.3,-8.437)

var _position: Vector3
var _velocity: Vector3
#var _direction: Vector3 = Vector3.ZERO
var _strong_direction: Vector3 = Vector3.BACK

var position_before_sync: Vector3
var jump_pos: Vector3
var jump_vel: Vector3
var last_sync_time_ms := 0
var sync_delta := 0.0
var start_time := 0

var has_collision := true


## Internal Variables
var _is_dragging := false
var _camera_input_direction := Vector2.ZERO
var _last_movement_direction := Vector3.BACK
var _gravity := -30.0
@onready var move_direction := Vector3.ZERO

var _camera_rotation_velocity := Vector2.ZERO
var _smoothed_camera_input := Vector2.ZERO
@export var camera_smoothness := 30.0  # higher = smoother


@onready var _camera_pivot: Node3D = %CameraPivot
@onready var _camera: Camera3D = %Camera3D
@onready var _skeleton: Node3D = %Skeleton_01
@onready var _animation_tree: AnimationTree = $AnimationTree
@onready var _dust_particles: GPUParticles3D = %DustParticles
@onready var _synchronizer: MultiplayerSynchronizer = $MultiplayerSynchronizer
@onready var _character_skin: CharacterSkin = %Skeleton_01

var target_pos : Vector3
var target_vel : Vector3
var target_direction : Vector3
var target_strong_direction : Vector3

var do_jump := 0

@onready var animation_player: AnimationPlayer = %AnimationPlayer
@onready var muzzle_flash: GPUParticles3D = $Skeleton_01/Pistol/MuzzleFlash
@onready var raycast: RayCast3D = $Skeleton_01/RayCast3D
var health := 100
@onready var health_text: Label3D = $Skeleton_01/health_text
var is_dead := false
@onready var pistol: Node3D = $Skeleton_01/Pistol

var has_bomb := false
@onready var bomb: Node3D = %Bomb
var explosion_count := 0  # Track how many times this player has exploded

var start_time_ping := 0
@onready var ping: Label = %ping

@onready var bomb_area: Area3D = %BombArea
@onready var bomb_timer: Label = %TimerLabel
var explosion_countdown := 0
var bomb_explosion_timer: Timer  # Keep reference to timer so it doesn’t get garbage collected

@onready var exit: Button = %Exit
@export var lod_bias_for_character: float = 10.0

var skin_parameters: Array = [0,0,0,0,0,0,0,0,0,0,0]
@onready var fps: Label = %fps
@onready var sparks: GPUParticles3D = %sparks
@onready var vfx_power_up: Node3D = %VFX_PowerUp
var _was_in_air := false # <--- ADD THIS LINE
@onready var jump_sfx: AudioStreamPlayer3D = %JumpSFX
var _is_moving := false # <--- ADD THIS LINE
@onready var water: AudioStreamPlayer3D = %Water
var player_id := 0
var _last_touch_position := Vector2.ZERO
var _camera_touch_index := -1  # Track which touch index is used for camera

var current_message_timer: Timer = null




func _enter_tree() -> void:
	set_multiplayer_authority(str(name).to_int())

func _ready() -> void:
	player_id = Global.global_player_id
	#Events.connect("kill_plane_touched",Callable(self,"respawn_at_start"))
	#TODO
	_apply_lod_bias_recursively(self)
	print("PLAYER ID IS : " + str(player_id))
	#health_text.text = str(player_id)
	#health_text.show()  # Make sure health_text is visible for messages
	#_set_player_skin.rpc()

	# Initialize camera touch tracking
	_reset_camera_touch()

	exit.hide()
	#TEST
	#_camera.current = true
	#Input.mouse_mode = Input.MOUSE_MODE_VISIBLE
	if is_multiplayer_authority():
		Input.mouse_mode = Input.MOUSE_MODE_VISIBLE
		_camera.current = true
		ping.show()

		set_skin_on_server.rpc(Global.skin_parameters)
		# Sync TCP ID to all clients after a frame delay to ensure multiplayer is ready
		await get_tree().process_frame
		sync_tcp_id.rpc(get_multiplayer_authority(), player_id)
	else:
		ping.hide()  # Hide ping label for non-authority players
	##health_text.text = str(health)

 # TODO
func _apply_lod_bias_recursively(node: Node):
	# Check if the current node is a mesh.
	if node is MeshInstance3D:
		# If it is, set its LOD bias.
		node.lod_bias = lod_bias_for_character
		#print("Applied LOD bias to: ", node.name)

	# Go through all children of the current node and run this function on them.
	for child in node.get_children():
		_apply_lod_bias_recursively(child)

# STEP 1: This function is called by the client and runs ONLY on the server.
@rpc("authority", "call_local", "reliable")
func set_skin_on_server(skins_from_client: Array):
	apply_skin_on_all_clients.rpc(skins_from_client)


@rpc("any_peer", "call_local", "reliable")
func apply_skin_on_all_clients(skins_from_server: Array):
	# Store the definitive skin data that came from the server.
	self.skin_parameters = skins_from_server
	
	var skin_meshes := get_node("Skeleton_01/Skeleton3D/").get_children()

	# Remove last 2 skins if unwanted
	skin_meshes = skin_meshes.slice(0, skin_meshes.size() - 2)

	for i in range(min(skin_meshes.size(), skins_from_server.size())):
		var skin = skin_meshes[i]
		var param_index = skins_from_server[i]

		var mesh_child = get_node("Skeleton_01/Skeleton3D/" + str(skin.name))

		if param_index == 0:
			# Clear mesh if index is 0
			mesh_child.mesh = null
			#print("Cleared mesh for: ", skin.name)
		else:
			# Build path to file
			var resource_path = "user://scenes/NewPlayer/resources/".path_join(str(skin.name))
			var file_path = resource_path.path_join(str(param_index) + ".tres")

			var resource = load(file_path)
			if resource is Mesh:
				mesh_child.mesh = resource
				#print("Applied: ", file_path)
			else:
				push_warning("Could not load mesh: %s" % file_path)



#region Display Ping
func send_ping()-> void:
	rpc_id(1, "_receive_ping", Time.get_ticks_msec())

@rpc("any_peer", "call_local")
func _receive_ping(ping_message)-> void:
	var sender_id = multiplayer.get_remote_sender_id()
	rpc_id(sender_id, "_receive_ping_response", ping_message)

@rpc("any_peer", "call_local")
func _receive_ping_response(ping_message)-> void:
	var round_trip_time = Time.get_ticks_msec() - ping_message
	ping.text = "ping: " + str(round_trip_time)
#endregion

# Sync TCP ID to all clients
@rpc("any_peer", "call_local")
func sync_tcp_id(multiplayer_id: int, tcp_id: int):
	# Forward this to the Game node to store the mapping
	var game_node = get_tree().get_root().get_node_or_null("Game")
	if game_node and game_node.has_method("store_player_tcp_id"):
		game_node.store_player_tcp_id(multiplayer_id, tcp_id)
	else:
		print("Warning: Could not find Game node or store_player_tcp_id method")

@rpc("any_peer","call_local")
func start_speed_timer():
	speed = 12
	%Footstep.pitch_scale = 1.2
	await get_tree().create_timer(5.0).timeout
	%Footstep.pitch_scale = 1.0
	print("TIMER IS UP!")
	speed = 10
	
	


@rpc("authority", "call_local")
func set_bomb_timer():
	if not multiplayer.is_server(): return
	explosion_countdown = randi_range(4, 8)
	bomb_explosion_timer = Timer.new()
	bomb_explosion_timer.timeout.connect(_on_bomb_timer_timeout)
	bomb_explosion_timer.wait_time = 1
	bomb_explosion_timer.autostart = true
	add_child(bomb_explosion_timer)
	rpc("_set_bomb_timer_all_clients", explosion_countdown)

func _on_bomb_timer_timeout():
	explosion_countdown -= 1
	rpc("_set_bomb_timer_all_clients", explosion_countdown)
	if explosion_countdown <= 0:
		bomb_explosion_timer.stop()
		bomb_explosion_timer.queue_free()
		bomb_explosion_timer = null
		rpc_id(get_multiplayer_authority(), "_on_bomb_exploded")
		await get_tree().create_timer(4.0).timeout
		set_bomb_timer.rpc_id(1)


func player_enter_water():
	%Water.play()
	await get_tree().create_timer(0.2).timeout
	explosion_countdown = 0
	rpc("_set_bomb_timer_all_clients", explosion_countdown)
	if has_bomb:
		bomb_explosion_timer.stop()
		bomb_explosion_timer.queue_free()
		bomb_explosion_timer = null
		rpc_id(get_multiplayer_authority(), "_on_bomb_exploded")
		await get_tree().create_timer(4.0).timeout
		set_bomb_timer.rpc_id(1)
	else:
		respawn_at_start()


@rpc("any_peer", "call_local") # This should be called by the server on the specific client
func _on_bomb_exploded():
	%Footstep.stop()
	%Skeleton3D.activate_ragdoll.rpc()
	_character_skin.set_no_bomb.rpc()
	if is_dead: return
	is_dead = true
	increment_explosion_count.rpc()
	
	var global_explosion_pos = global_position
	
	# Note: We now call the reassign RPC on the Game node, not on itself
	get_node("/root/Game").play_explosion_at.rpc(global_explosion_pos)
	
	await get_tree().create_timer(3.0).timeout 
	# The player tells the server it died, so the server can reassign the bomb
	get_node("/root/Game").reassign_bomb.rpc_id(1, get_multiplayer_authority())
	
	set_bomb_status(false) # Make sure bomb visual is hidden
	exit.show()
	#await get_tree().create_timer(5.0).timeout
	respawn_after_death()

@rpc("any_peer", "call_local")
func increment_explosion_count():
	explosion_count += 1
	print("Player %s exploded! Total explosions: %d" % [name, explosion_count])



# Remote Procedure Calls
@rpc("call_local", "any_peer")
func respawn_at_start():
	print("RESPAWNED!")
	_position = Vector3(-30,8,-10)
	position = Vector3(-30,8,-10)


@rpc
func update_jumps(new_position: Vector3,new_vel: Vector3)-> void:
	if not is_multiplayer_authority():
		jump_pos=new_position
		jump_vel=new_vel
		do_jump = 1

@rpc("unreliable")
func update_positions(new_position: Vector3,new_strong_direction: Vector3,new_velocity: Vector3):
	#print("get sync")
	target_pos = new_position
	target_strong_direction= new_strong_direction
	target_vel=new_velocity
	
@rpc("any_peer","call_local")
func play_shoot_effects():
	muzzle_flash.restart()
	muzzle_flash.emitting = true
	
@rpc("any_peer", "call_remote")
func request_damage(origin: Vector3, direction: Vector3, length: float):
	if not multiplayer.is_server():
		return
		
	# calculate direction of raycast for server.
	var space_state := get_world_3d().direct_space_state
	var query := PhysicsRayQueryParameters3D.new()
	query.from = origin
	query.to = origin + direction.normalized() * length
	query.collision_mask = 1

	var result = space_state.intersect_ray(query)

	# Check if the raycast hit a player
	if result and result.collider is Player:
		var hit_player: Player = result.collider
		hit_player.receive_damage.rpc()


@rpc("any_peer", "call_local")
func receive_damage():
	#print("player %s was hit" %str(get_multiplayer_authority()))
	health -= 10
	health_text.text = str(health)
	if health <= 50 and health > 20:
		health_text.modulate = Color(1.0, 0.864, 0.23)
	elif health <= 20:
		health_text.modulate = Color(1.0, 0.172, 0.248)
	if health <= 0 and not is_dead:
		health_text.hide()
		health = 100
		health_text.text = str(health)
		is_dead = true
		set_process(false)
		pistol.hide()
		_character_skin.die.rpc()
		
@rpc("any_peer", "call_local")
func send_local_message(message: String):
	print("Player %s displaying message: %s" % [str(get_multiplayer_authority()), message])

	# If a message timer is already running, kill it first
	if current_message_timer and current_message_timer.is_inside_tree():
		current_message_timer.queue_free()
		current_message_timer = null

	# Store the original health text and color
	var original_text = health_text.text
	var original_color = health_text.modulate

	# Display the message over player's head
	health_text.text = message
	health_text.modulate = Color.WHITE
	health_text.show()

	# Create a new timer for message timeout
	current_message_timer = Timer.new()
	current_message_timer.one_shot = true
	current_message_timer.wait_time = 5.0
	current_message_timer.timeout.connect(_on_message_timer_timeout.bind(current_message_timer, original_text, original_color))
	add_child(current_message_timer)
	current_message_timer.start()

func _on_message_timer_timeout(timer: Timer, original_text: String, original_color: Color):
	print("Player %s message timeout - restoring original text: %s" % [str(get_multiplayer_authority()), original_text])
	health_text.text = original_text
	health_text.modulate = original_color
	health_text.hide()
	timer.queue_free()
	
	# Clear the reference
	if timer == current_message_timer:
		current_message_timer = null


func _input(event: InputEvent) -> void:
	if event.is_action_pressed("ui_cancel"):
		Input.mouse_mode = Input.MOUSE_MODE_VISIBLE

	# Handle touch input for camera movement (mobile)
	if event is InputEventScreenTouch:
		# Check if this touch is NOT on the virtual joystick area
		if not _is_touch_on_virtual_joystick(event.position):
			if event.pressed:
				# Only start camera dragging if we don't already have a camera touch
				if _camera_touch_index == -1:
					_camera_touch_index = event.index
					_is_dragging = true
					_last_touch_position = event.position
			else:
				# Only stop camera dragging if this is our camera touch
				if event.index == _camera_touch_index:
					_camera_touch_index = -1
					_is_dragging = false

	# Handle touch drag for camera movement
	elif event is InputEventScreenDrag:
		# Only process drag if this is our designated camera touch
		if event.index == _camera_touch_index and _is_dragging:
			var relative_motion = event.position - _last_touch_position

			# Add deadzone to prevent tiny movements from causing jitter
			#if relative_motion.length() > 0.5:  # Minimum movement threshold
			_camera_input_direction.x = -relative_motion.x * mouse_sensitivity  # Reduce sensitivity
			_camera_input_direction.y = relative_motion.y * mouse_sensitivity    # Reduce sensitivity
			_last_touch_position = event.position

func _unhandled_input(event: InputEvent) -> void:
	# Handle mouse input for camera movement (desktop)
	if event is InputEventMouseButton and event.button_index == MOUSE_BUTTON_LEFT:
		_is_dragging = event.is_pressed()

	# If we are dragging, capture the mouse's relative motion
	if event is InputEventMouseMotion and _is_dragging:
		_camera_input_direction.x = -event.relative.x * mouse_sensitivity
		_camera_input_direction.y = event.relative.y * mouse_sensitivity

	# Keep your original shooting logic
	#if Input.is_action_just_pressed("shoot") \
		#and animation_player.current_animation != "shoot":
		#play_shoot_effects.rpc()
		#request_damage.rpc_id(1, raycast.global_transform.origin, raycast.global_transform.basis.z, 20.0)

# Helper function to check if touch is on virtual joystick area
func _is_touch_on_virtual_joystick(touch_position: Vector2) -> bool:
	# Get the virtual joystick node - adjust the path as needed
	var virtual_joystick = get_node_or_null("/root/Game/UI/Virtual Joystick")
	if virtual_joystick:
		return virtual_joystick.is_point_inside_joystick_area(touch_position)
	return false

# Reset camera touch tracking (call this if needed)
func _reset_camera_touch():
	_camera_touch_index = -1
	_is_dragging = false
	_camera_input_direction = Vector2.ZERO


# NEW: _process handles camera logic so it's independent from physics.
func _process(delta: float) -> void:
	# Smooth camera input
	_smoothed_camera_input = _smoothed_camera_input.lerp(_camera_input_direction, camera_smoothness * delta)
	
	# Apply smoothed camera rotation
	_camera_pivot.rotation.x = clamp(_camera_pivot.rotation.x + _smoothed_camera_input.y * delta, -PI / 25, PI / 4.0)
	_camera_pivot.rotation.y += _smoothed_camera_input.x * delta
	
	# Slowly decay input to zero
	_camera_input_direction = Vector2.ZERO


func _physics_process(delta: float) -> void:
	#health_text.text = str(health)
	fps.text = str(Engine.get_frames_per_second())
	#TEST
	#_process_authority(delta)
	if is_multiplayer_authority():
		_process_authority(delta)
		_set_sync_properties()
	else:
		_interpolate_client(delta)

func _process_authority(delta: float) -> void:

	# Movement calculation
	var raw_input := Input.get_vector("move_left", "move_right", "move_up", "move_down")

	var forward := _camera.global_basis.z
	var right := _camera.global_basis.x
	move_direction = (forward * raw_input.y + right * raw_input.x)
	move_direction.y = 0
	move_direction = move_direction.normalized()

	var is_just_jumping := Input.is_action_just_pressed("jump") and is_on_floor()
	if is_just_jumping:
		rpc("update_jumps", position,velocity)  # Send RPC first with current position
		%JumpSFX.play()
		velocity.y += jump_impulse

	if not is_on_floor():
		velocity.y += _gravity * delta

	var horizontal_velocity = Vector3(velocity.x, 0, velocity.z)
	horizontal_velocity = horizontal_velocity.move_toward(move_direction * speed, acceleration * delta)

	velocity.x = horizontal_velocity.x
	velocity.z = horizontal_velocity.z

	move_and_slide()
	
	# Check if player was in air to play land sound
	if is_on_floor() and _was_in_air:
		%LandSFX.play()
	_was_in_air = not is_on_floor()

	# Character facing direction
	if move_direction.length() >= 0.2:
		_last_movement_direction = move_direction
	var tgt := Vector3.BACK.signed_angle_to(_last_movement_direction, Vector3.UP)
	_skeleton.global_rotation.y = lerp_angle(_skeleton.global_rotation.y, tgt, rotation_speed * delta)

	var xz_speed := Vector2(velocity.x, velocity.z).length()
	var on_floor := is_on_floor()

	if is_just_jumping or not on_floor:
		if _is_moving:
			%Footstep.stop()
			_is_moving = false
		if not has_bomb:
			_character_skin.jump.rpc()
		else:
			_character_skin.jump_bomb.rpc()
		
	elif not on_floor and velocity.y < 0:
		pass
# NEW CORRECTED CODE
	elif on_floor and not is_dead:
		# Determine if the player is currently moving on the ground
		var is_currently_moving = xz_speed > 0.1

		# --- Sound Logic ---
		# Player JUST STARTED moving: Play the sound
		if is_currently_moving and not _is_moving:
			%Footstep.play()
		# Player JUST STOPPED moving: Stop the sound
		elif not is_currently_moving and _is_moving:
			%Footstep.stop()

		# --- Animation Logic (remains the same) ---
		if is_currently_moving:
			if not has_bomb:
				_character_skin.set_moving.rpc(true)
				_character_skin.set_moving_speed.rpc(inverse_lerp(0.0, speed, xz_speed))
			else:
				_character_skin.bomb_hold.rpc(true)
				_character_skin.set_moving_speed.rpc(inverse_lerp(0.0, speed, xz_speed))
		else:
			if not has_bomb:
				_character_skin.set_moving.rpc(false)
			else:
				_character_skin.bomb_hold.rpc(false)
		

		# IMPORTANT: Update the state for the next frame
		_is_moving = is_currently_moving

	# Dust VFX
	_dust_particles.emitting = on_floor and xz_speed > 0 and not is_dead



func handle_jump_movement(delta: float) -> void:
	# نادیده گرفتن محور y برای محاسبه فاصله
	var jump_pos_flat = Vector3(jump_pos.x, global_transform.origin.y, jump_pos.z)
	var direction = (jump_pos_flat - global_transform.origin).normalized()
	var distance = global_transform.origin.distance_to(jump_pos_flat)
	
	if distance > 0.1:
		# حرکت نرم به سمت jump_pos
		velocity.x = lerp(velocity.x, float(direction.x * speed), float(acceleration * delta))
		velocity.z = lerp(velocity.z, float(direction.z * speed), float(acceleration * delta))
		if abs(velocity.x) < 0.1:
			velocity.x = 0
		if abs(velocity.z) < 0.1:
			velocity.z = 0
	else:
		velocity.x = lerp(velocity.x,jump_pos.x, float(acceleration * delta))
		velocity.z = lerp(velocity.z, jump_pos.z, float(acceleration * delta))
		
		position = jump_pos
		velocity=jump_vel
		perform_jump()

func perform_jump() -> void:
	velocity.y += jump_impulse
	do_jump = 0
	if not has_bomb:
		_character_skin.jump()
	else:
		_character_skin.jump_bomb()

func _interpolate_client(delta: float) -> void:
	_orient_character_to_direction(target_strong_direction, delta)
	
	if global_transform.origin.distance_to(target_pos) > 5:  # Reduced from 5 to 2
		position = target_pos
	
	if do_jump==1:
		handle_jump_movement(delta)
	else :
		move_towards_target(delta)
		
	if not is_on_floor():
		velocity.y += _gravity * delta
	move_and_slide()

func move_towards_target(delta: float) -> void:
	# نادیده گرفتن محور y در target_pos
	var target_pos_flat = Vector3(target_pos.x, target_pos.y, target_pos.z)
	#var direction = (target_pos_flat - global_transform.origin).normalized()
	var distance = global_transform.origin.distance_to(target_pos_flat)

	if distance > 0.01:  # اگر فاصله از هدف بیشتر از حداقل فاصله باشد
		position = position.lerp(target_pos_flat, 5 * delta)
		velocity.x = target_vel.x
		velocity.z = target_vel.z
		_dust_particles.emitting = is_on_floor()
	else:
		velocity.x = target_vel.x
		velocity.z = target_vel.z
		position=target_pos
		_dust_particles.emitting = false


func _set_sync_properties() -> void:
	_position = position
	_velocity = velocity
	_strong_direction = _last_movement_direction
	var current_time = Time.get_ticks_msec()
	if current_time - start_time >= 100:
		rpc("update_positions", _position,_strong_direction,_velocity)
		start_time = current_time
	if current_time - start_time_ping >=1000:
		send_ping()
		start_time_ping = current_time


func _orient_character_to_direction(direction: Vector3, delta: float) -> void:
	if direction == Vector3.ZERO:
		return  # از تقسیم بر صفر یا cross محصول نال جلوگیری می‌کنه
  
	direction = direction.normalized()
	var left_axis := Vector3.UP.cross(direction)
  
	var new_basis := Basis(left_axis, Vector3.UP, direction).orthonormalized()
	var new_quat := new_basis.get_rotation_quaternion()
  
	var current_basis := _skeleton.transform.basis
	var current_quat := current_basis.get_rotation_quaternion()
	var interpolated_quat := current_quat.slerp(new_quat, delta * rotation_speed)
  
	var scales := current_basis.get_scale()
	_skeleton.transform.basis = Basis(interpolated_quat).scaled(scales)

func respawn_after_death():
	is_dead = false
	#set_process(true)
	%Skeleton3D.deactivate_ragdoll.rpc()
	#muzzle_flash.get_parent().visible = true  # Show pistol again
	#health_text.modulate = Color(1.0, 1.0, 1.0)
	#health_text.show()
	respawn_at_start()


func _on_animation_tree_animation_finished(anim_name: StringName) -> void:
	if anim_name == "Death_Forward":
		pass
		#await get_tree().create_timer(1.0).timeout
		#respawn_after_death()

#region Bomb Game RPCs
@rpc("any_peer", "call_local")
func set_bomb_status(status: bool):
	has_bomb = status

# This function will enable or disable the bomb transfer area.
@rpc("any_peer", "call_local")
func toggle_bomb_area(is_enabled: bool):
	bomb_area.monitoring = is_enabled
#endregion

@rpc("any_peer","call_local")
func toggle_spark():
	sparks.emitting = true
	%BombTransfer.play()

func _on_bomb_area_body_entered(body: Node3D) -> void:
	#%BombTransfer.play()
	# These checks ensure only the player with the bomb can initiate a transfer
	if not is_multiplayer_authority() or not has_bomb:
		return
		
	if body is Player and body != self:
		# Call the NEW RPC on the Game node instead of the old one on the player.
		get_node("/root/Game").request_bomb_transfer.rpc_id(1, body.get_multiplayer_authority())
		toggle_spark.rpc()
		


func _on_exit_pressed() -> void:
	Tcpserver.send_leavegame(player_id)
	MultiplayerController.is_peer_connected = false
	
	# Properly disconnect the multiplayer peer before changing scenes
	if multiplayer.multiplayer_peer:
		multiplayer.multiplayer_peer.close()
		multiplayer.multiplayer_peer = null
	
	get_tree().change_scene_to_file("res://game_over.tscn")
