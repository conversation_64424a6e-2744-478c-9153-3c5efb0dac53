extends Node2D

const online_players := preload("res://scenes/UI/Friends/online_players.tscn")
const message := preload("res://scenes/UI/PrivateChat/private_message_box.tscn")

@onready var scroll_container: ScrollContainer = $ChatPrivate/ScrollContainer
@onready var message_list: VBoxContainer = $ChatPrivate/ScrollContainer/MessageList


@onready var panel: Panel = $ChatPrivate/Panel3

@onready var text_edit: TextEdit = $ChatPrivate/Panel3/TextEdit

@onready var send: Button = $ChatPrivate/Send
@onready var user_name: Label = $ChatPrivate/user_name
@onready var label: Label = $ChatPrivate/Label
@onready var online_players_container: VBoxContainer = $ChatPrivate/online_friends/ScrollContainer/OnlinePlayersContainer
@onready var check_key_board_focus_timer: Timer = $CheckKeyBoardFocusTimer


var database: SQLite
var scrolling := false
var scroll_target := 0.0
var auto_scroll_enabled := true
var initial_panel_position := Vector2()
var initial_send_position := Vector2()
var is_me := true
var hid : int
var input_text := ""
var hid_new = 0

var keyboard_init := false
var kb_height = 0


func _ready() -> void:
	user_name.text = Global.friend_name
	Tcpserver.connect("frchat_received", Callable(self, "_on_frchat_received"))
	Tcpserver.connect("frget_received", Callable(self, "_on_frget_received"))
	Tcpserver.send_request("frget")
	text_edit.gui_input.connect(_on_text_edit_gui_input)
	text_edit.focus_entered.connect(_on_text_edit_focused)
	text_edit.focus_exited.connect(_on_text_edit_unfocused)
	scroll_container.scroll_started.connect(_on_user_scroll)

	# Store initial positions
	initial_panel_position = panel.position
	initial_send_position = send.position
	#var dir = DirAccess.open("user://")
	#if dir.file_exists("chat.db"):
		#dir.remove("chat.db")
	
	# Setup DB
	database = SQLite.new()
	database.path = "user://chat.db"
	database.open_db()
	#create_chats_table()
	# Read Messages
	load_messages()
	database.update_rows("chats", "hid = '" + str(Global.hid) + "'",{"read" :1})
	
func _on_text_edit_gui_input(event: InputEvent) -> void:
	if event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
		#print("Node clicked: ", text_edit.name, ", Type: ", text_edit.get_class())
		if not text_edit.has_focus():
			text_edit.grab_focus() # Ensure focus is set if not already focused
		get_viewport().set_input_as_handled() # Prevent event from propagating further

#func _notification(what):
	#match what:
		#NOTIFICATION_WM_GO_BACK_REQUEST:
			#print("BACK PRESSED")
			##if text_edit.has_focus():
				##text_edit.release_focus()
				##DisplayServer.virtual_keyboard_hide()
			##else:
				##_on_back_pressed()  # fallback to normal back navigation

func _process(delta: float) -> void:
	if scrolling and auto_scroll_enabled:
		var scrollbar = scroll_container.get_v_scroll_bar()
		scrollbar.value = lerp(scrollbar.value, scroll_target, delta * 8.0)
		if abs(scrollbar.value - scroll_target) < 1.0:
			scrollbar.value = scroll_target
			scrolling = false

func _input(event: InputEvent) -> void:
	if event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
		if not text_edit.get_global_rect().has_point(event.position) and text_edit.has_focus():
			text_edit.release_focus()

func format_duration(milliseconds: float) -> String:
	var unix_time = int(milliseconds / 1000)
	var dt = get_tehran_datetime_dict_from_unix_time(unix_time)
	
	var hour = dt["hour"]
	var minute = dt["minute"]
	
	var g_year = dt["year"]
	var g_month = dt["month"]
	var g_day = dt["day"]

	var j_date = gregorian_to_jalali(g_year, g_month, g_day)
	var j_year = j_date[0]
	var j_month = j_date[1]
	var j_day = j_date[2]
	
	return "%02d:%02d  %04d/%02d/%02d" % [hour, minute, j_year, j_month, j_day]

	
func get_tehran_datetime_dict_from_unix_time(unix_time: int) -> Dictionary:
	# زمان تهران معمولاً UTC+3:30 یا +4:30 بسته به فصل (بدون DST الان فرض می‌گیریم +3:30)
	# اگر تغییر ساعت تابستانی مد نظرته بگو تا اضافه کنم
	var tehran_offset_seconds = 3 * 3600 + 1800  # +3:30
	var tehran_time = unix_time + tehran_offset_seconds
	return Time.get_datetime_dict_from_unix_time(tehran_time)


# تبدیل تاریخ میلادی به شمسی
func gregorian_to_jalali(gy:int, gm:int, gd:int) -> Array:
	var g_d_m = [0,31,59,90,120,151,181,212,243,273,304,334]
	var jy = 0
	var jm = 0
	var jd = 0

	var gy2 = (gy + 1) if (gm > 2) else gy
	var days = 355666 + (365 * gy) + ((gy2 + 3) / 4) - ((gy2 + 99) / 100) + ((gy2 + 399) / 400) + gd + g_d_m[gm - 1]
	
	jy = -1595 + 33 * (days / 12053)
	days %= 12053
	jy += 4 * (days / 1461)
	days %= 1461

	if days > 365:
		jy += (days - 1) / 365
		days = (days - 1) % 365

	if days < 186:
		jm = 1 + (days / 31)
		jd = 1 + (days % 31)
	else:
		jm = 7 + ((days - 186) / 30)
		jd = 1 + ((days - 186) % 30)

	return [jy, jm, jd]


func _on_send_pressed() -> void:
	input_text = text_edit.text.strip_edges()
	
	if input_text != "":
		Tcpserver.send_message(input_text)
		
	text_edit.clear()
	_on_text_edit_unfocused()
		
func _on_frchat_received(data: Dictionary):
	print("CHAT DATA RECIEVED : " + str(data))
	if str(Global.global_player_id) == data["id"]:
		is_me = true
	else: is_me = false
	
	var msg = message.instantiate()
	message_list.add_child(msg)
	#var date_time = msg.set_time()
	#msg.time.text =  format_duration(data["time"])
	msg.set_params(data["msg"], is_me,format_duration(data["time"]))
	msg.message_deleted.connect(_on_message_deleted)
	
	text_edit.clear()
	
	#var db_data = {
		#"name": data["name"],
		#"message": data["msg"],
		#"hid": data["hid"],
		#"ax": data["ax"],
		#"read": 1,
		#"date": data["time"],
		#"me": is_me
	#}
	#
	#database.insert_row("chats", db_data)
	#print("CHAT INSERTED!" + str(db_data))
	auto_scroll_enabled = true
	await get_tree().process_frame
	text_edit.release_focus()

	await get_tree().process_frame
	scroll_target = scroll_container.get_v_scroll_bar().max_value
	scrolling = true



func _on_message_deleted(date_time: String) -> void:
	database.delete_rows("chats", "date = '" + date_time + "'")
	ToastParty.show({
		"text": "    Message Deleted    ",
		"bgcolor": Color(1.0, 0.163, 0.193, 0.435),
		"color": Color(1, 1, 1, 1),
		"gravity": "top",
		"direction": "center",
		"text_size": 24,
		"use_font": true
	})


	
	
func load_messages():
	database.query("SELECT name FROM sqlite_master WHERE type='table' AND name='chats';")
	if database.query_result.size() == 0:
		create_chats_table()
	
	var list = database.select_rows("chats", "hid = '" + str(Global.hid) + "'", ["*"])
	for row in list:
		var msg = message.instantiate()
		message_list.add_child(msg)
		msg.set_params(row["message"], bool(row["me"]) , format_duration(float(row["date"])))
		msg.message_deleted.connect(_on_message_deleted)
	await get_tree().process_frame
	scroll_target = scroll_container.get_v_scroll_bar().max_value
	scrolling = true
	
func _on_user_scroll() -> void:
	auto_scroll_enabled = false
#await get_tree().create_timer(0.5).timeout


func _on_text_edit_focused() -> void:
	var viewport_size = get_viewport_rect().size
	if OS.get_name() == "Android":
		if !keyboard_init:
			await get_tree().create_timer(1).timeout
			kb_height = DisplayServer.virtual_keyboard_get_height()
			var target_y = panel.position.y - kb_height + panel.size.y + 40.0
			var tween = create_tween()
			tween.tween_property(panel, "position:y", target_y, 0.25).set_trans(Tween.TRANS_QUAD).set_ease(Tween.EASE_OUT)
			tween.parallel().tween_property(send, "position:y", target_y, 0.25).set_trans(Tween.TRANS_QUAD).set_ease(Tween.EASE_OUT)
			keyboard_init = true
			check_key_board_focus_timer.start()
		else:
			if kb_height > 0:
				await get_tree().process_frame
				var target_y = panel.position.y - kb_height + panel.size.y + 40.0
				var tween = create_tween()
				tween.tween_property(panel, "position:y", target_y, 0.25).set_trans(Tween.TRANS_QUAD).set_ease(Tween.EASE_OUT)
				tween.parallel().tween_property(send, "position:y", target_y, 0.25).set_trans(Tween.TRANS_QUAD).set_ease(Tween.EASE_OUT)
				check_key_board_focus_timer.start()
	#else:
		## fallback
		#panel.position.y = viewport_size.y / 2.5
		#send.position.y = viewport_size.y / 2.5



func _on_text_edit_unfocused() -> void:
	await get_tree().process_frame
	var tween = create_tween()
	tween.tween_property(panel, "position", initial_panel_position, 0.25).set_trans(Tween.TRANS_QUAD).set_ease(Tween.EASE_OUT)
	tween.parallel().tween_property(send, "position", initial_send_position, 0.25).set_trans(Tween.TRANS_QUAD).set_ease(Tween.EASE_OUT)
	check_key_board_focus_timer.stop()
	var style: StyleBox = text_edit.get("theme_override_styles/normal")
	
	if style is StyleBoxEmpty:
		var style_empty := style as StyleBoxEmpty
		style_empty.content_margin_top = 44



func _on_back_pressed() -> void:
	Global.return_from_chat = true
	get_tree().change_scene_to_file("res://scenes/UI/Friends/friends_no_background.tscn")


func _on_text_edit_text_changed() -> void:
	var line_count = text_edit.get_line_count()

	# Prevent more than 2 lines
	if line_count > 2:
		var lines := text_edit.text.split("\n", false)
		text_edit.text = "\n".join(lines.slice(0, 2))
		text_edit.set_caret_line(text_edit.get_line_count() - 1)
		text_edit.set_caret_column(text_edit.get_line(text_edit.get_line_count() - 1).length())

	
	# Adjust style margin as before
	var style: StyleBox = text_edit.get("theme_override_styles/normal")
	if style is StyleBoxEmpty:
		var style_empty := style as StyleBoxEmpty
		style_empty.content_margin_top = -1 if (line_count > 1) else 44

			

func create_chats_table():
	var table = {
		"id": {"data_type": "int", "primary_key": true, "not_null": true, "auto_increment": true},
		"name": {"data_type": "text"},
		"message": {"data_type": "text"},
		"hid": {"data_type": "int"},
		"ax": {"data_type": "text"},
		"read": {"data_type": "int"},
		"date": {"data_type": "real"}, # store Unix timestamps (float)
		"me": {"data_type": "int"}     # store bool as int: 0 or 1
	}
	database.create_table("chats", table)
	
func get_chats():
	database.query("SELECT * FROM chats")
	print(database.query_result)
	label.text = str(database.query_result.size()) + "---" + str(Global.hid)


func _on_button_pressed() -> void:
	get_chats()


func _on_button_2_pressed() -> void:
	database.delete_rows("chats" , "*")


func _on_get_online_friends_timeout() -> void:
	Tcpserver.send_request("frget")
	

	

func _on_frget_received(data:Dictionary):
	get_tree().call_group("online_players" , "queue_free")
	var users: Array = data.get("lists",[]).duplicate()
	for i in range(users.size()):
		var user_data = users[i]
		var fields = user_data.split("|")
		if fields.size() < 6:
			continue

		var number = fields[0]
		var player_id = fields[1]
		var user_name_str = fields[2]
		var profile_picture_path = fields[3]
		var online = fields[4]
		var last_seen = fields[5]

		if online == "1":
			var card = online_players.instantiate()
			online_players_container.add_child(card)

			card.username.text = user_name_str
			card.id.text = str(player_id)
				# SET PROFILE PICTURE
			if profile_picture_path != "0":
				var image_path = "res://assets/profiles/" + profile_picture_path
				if ResourceLoader.exists(image_path):
						#card.profile.texture = load(image_path)
					pass
				else:
					card.profile.texture = load("res://assets/images/profile_placeHolder.png")
			else:
				card.profile.texture = load("res://assets/images/profile_placeHolder.png")
	


func _on_check_key_board_focus_timeout() -> void:
	var check_kb_height = DisplayServer.virtual_keyboard_get_height()
	if check_kb_height  <= 0:
		text_edit.release_focus()
		_on_text_edit_unfocused()
