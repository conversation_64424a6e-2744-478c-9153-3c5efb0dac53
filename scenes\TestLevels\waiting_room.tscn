[gd_scene load_steps=224 format=4 uid="uid://st6ylijxxsnv"]

[ext_resource type="Script" uid="uid://mbqy45ptgd66" path="res://scripts/waiting_room.gd" id="1_xsplj"]
[ext_resource type="Script" uid="uid://pjx4121v60s7" path="res://scripts/multiplayer/multiplayer_spawner.gd" id="2_wan5x"]
[ext_resource type="PackedScene" uid="uid://bi6p0jpgoims" path="res://assets/nature/crops_bambooStageA.glb" id="3_gyvjn"]
[ext_resource type="PackedScene" uid="uid://qs0u0x1irblq" path="res://assets/nature/crops_bambooStageB.glb" id="4_3eurq"]
[ext_resource type="PackedScene" uid="uid://dsstx6q5a4fvc" path="res://assets/nature/crops_cornStageA.glb" id="5_dpnkx"]
[ext_resource type="PackedScene" uid="uid://5axp1om5omh6" path="res://assets/nature/crops_leafsStageA.glb" id="6_60ey1"]
[ext_resource type="PackedScene" uid="uid://by806dqvgiiiy" path="res://assets/nature/crops_leafsStageB.glb" id="7_kocix"]
[ext_resource type="PackedScene" uid="uid://cqsd5o8y5q0k4" path="res://assets/nature/crops_wheatStageA.glb" id="8_g2t6w"]
[ext_resource type="PackedScene" uid="uid://bocem6rbych4m" path="res://assets/nature/crops_wheatStageB.glb" id="9_weblv"]
[ext_resource type="PackedScene" uid="uid://dl3g3ghryumoq" path="res://assets/nature/grass.glb" id="10_nmen0"]
[ext_resource type="PackedScene" uid="uid://cv6qnqo4gm6n2" path="res://assets/nature/grass_large.glb" id="11_xdkde"]
[ext_resource type="PackedScene" uid="uid://cew7e5xnkffpy" path="res://assets/nature/grass_leafs.glb" id="12_s41v5"]
[ext_resource type="PackedScene" uid="uid://bxk6vqc5pd6tn" path="res://assets/nature/grass_leafsLarge.glb" id="13_xm6xh"]
[ext_resource type="PackedScene" uid="uid://b4tvrxgmjo4ab" path="res://assets/nature/ground_pathRocks.glb" id="14_0mkfr"]
[ext_resource type="PackedScene" uid="uid://cvnmjfno08iqs" path="res://assets/nature/ground_grass.glb" id="15_xqrf6"]
[ext_resource type="PackedScene" uid="uid://dwi7pbh25otex" path="res://assets/nature/cliff_block_rock.glb" id="16_eh8g7"]
[ext_resource type="PackedScene" uid="uid://b5bbcui7ge7ac" path="res://assets/nature/cliff_top_rock.glb" id="17_e0407"]
[ext_resource type="PackedScene" uid="uid://c2wxvis0s47bq" path="res://assets/nature/platform_grass.glb" id="18_14tf0"]
[ext_resource type="PackedScene" uid="uid://d6ocl0fk5tgi" path="res://assets/nature/path_stone.glb" id="19_puwsv"]
[ext_resource type="PackedScene" uid="uid://bcobhnj46gvtl" path="res://assets/nature/path_stoneCircle.glb" id="20_0t4ge"]
[ext_resource type="PackedScene" uid="uid://bbxlk6y8qkc6f" path="res://assets/nature/path_stoneCorner.glb" id="21_e5fwn"]
[ext_resource type="PackedScene" uid="uid://ouhtug1jx6y1" path="res://assets/nature/path_stoneEnd.glb" id="22_c8mww"]
[ext_resource type="PackedScene" uid="uid://dba63y88700sw" path="res://assets/nature/statue_ring.glb" id="23_1b17j"]
[ext_resource type="PackedScene" uid="uid://c33jypiti78m8" path="res://assets/nature/bridge_woodRoundNarrow.glb" id="24_enwkh"]
[ext_resource type="PackedScene" uid="uid://bumwboatbwgui" path="res://assets/nature/bridge_side_woodRound.glb" id="25_gxvs6"]
[ext_resource type="PackedScene" uid="uid://bpbvi3k4cuw8s" path="res://assets/nature/bridge_center_woodRound.glb" id="26_y6o7m"]
[ext_resource type="PackedScene" uid="uid://do4om77l4qpw" path="res://assets/nature/ground_riverSideOpen.glb" id="27_0bqvo"]
[ext_resource type="PackedScene" uid="uid://3s0edmxbyun7" path="res://assets/nature/ground_riverSide.glb" id="28_hauqx"]
[ext_resource type="PackedScene" uid="uid://bl77102bk24ee" path="res://assets/nature/ground_riverCorner.glb" id="29_3jeqs"]
[ext_resource type="PackedScene" uid="uid://df46qy3f3wxh5" path="res://assets/nature/ground_riverOpen.glb" id="30_awkkw"]
[ext_resource type="PackedScene" uid="uid://c1ub6u6ngx1r5" path="res://assets/nature/cliff_blockHalf_stone.glb" id="31_ckhmy"]
[ext_resource type="PackedScene" uid="uid://d24eohmw55mil" path="res://assets/nature/cliff_waterfallTop_stone.glb" id="32_c2m0n"]
[ext_resource type="PackedScene" uid="uid://jtynit2pmknv" path="res://assets/nature/cliff_top_stone.glb" id="33_cre7x"]
[ext_resource type="PackedScene" uid="uid://bviso8qadgqdj" path="res://assets/nature/cliff_waterfallTop_rock.glb" id="34_ojfpl"]
[ext_resource type="PackedScene" uid="uid://cevjaeprm88nc" path="res://assets/nature/cliff_topDiagonal_rock.glb" id="35_k1niv"]
[ext_resource type="PackedScene" uid="uid://dgj632tbql3y5" path="res://assets/nature/tree_blocks_dark.glb" id="36_ifv8m"]
[ext_resource type="PackedScene" uid="uid://kbs17u4b1m5f" path="res://assets/nature/tree_cone_dark.glb" id="37_272hi"]
[ext_resource type="PackedScene" uid="uid://bmhpuj5vaepuk" path="res://assets/nature/tree_default_dark.glb" id="38_cldpd"]
[ext_resource type="PackedScene" uid="uid://dp2ay8ajfrrwy" path="res://assets/nature/tree_detailed_dark.glb" id="39_nkln6"]
[ext_resource type="PackedScene" uid="uid://35ewa6t1lag5" path="res://assets/nature/tree_fat_darkh.glb" id="40_447mi"]
[ext_resource type="PackedScene" uid="uid://capl0oj2luvd8" path="res://assets/nature/tree_oak_dark.glb" id="41_8b7cp"]
[ext_resource type="PackedScene" uid="uid://c8c4q0difxw5s" path="res://assets/nature/tree_pineDefaultB.glb" id="42_54af6"]
[ext_resource type="PackedScene" uid="uid://c4j3oi1y4xq0s" path="res://assets/nature/tree_pineRoundD.glb" id="43_b5052"]
[ext_resource type="PackedScene" uid="uid://iri1nfg5578m" path="res://assets/nature/tree_pineRoundE.glb" id="44_jep2d"]
[ext_resource type="PackedScene" uid="uid://b68fd7d0pkbxj" path="res://assets/nature/tree_pineTallC_detailed.glb" id="45_wadkt"]
[ext_resource type="PackedScene" uid="uid://c8yjl075k618a" path="res://assets/nature/tree_pineGroundB.glb" id="46_jbech"]
[ext_resource type="PackedScene" uid="uid://c0fci6mcebgqc" path="res://assets/nature/tree_pineRoundC.glb" id="47_h82sl"]
[ext_resource type="PackedScene" uid="uid://dpi6j228mmm2c" path="res://assets/nature/tree_pineSmallC.glb" id="48_8nthy"]
[ext_resource type="PackedScene" uid="uid://b3wtte4dmcbjm" path="res://assets/nature/tree_pineTallA_detailed.glb" id="49_1xtvl"]
[ext_resource type="PackedScene" uid="uid://rl6fl6xau42v" path="res://assets/nature/tree_pineTallB.glb" id="50_ntc7p"]
[ext_resource type="PackedScene" uid="uid://ciwfe2votdqoq" path="res://assets/nature/stone_largeF.glb" id="51_3ik7o"]
[ext_resource type="PackedScene" uid="uid://fsu80pfo8hod" path="res://assets/nature/stone_smallA.glb" id="52_nll44"]
[ext_resource type="PackedScene" uid="uid://bua1df58qj81n" path="res://assets/nature/stone_smallB.glb" id="53_3gjjd"]
[ext_resource type="PackedScene" uid="uid://dn2hv4xbuccg7" path="res://assets/nature/stone_smallC.glb" id="54_mqwrx"]
[ext_resource type="PackedScene" uid="uid://b5t73qru88yby" path="res://assets/nature/stone_smallD.glb" id="55_3xp12"]
[ext_resource type="PackedScene" uid="uid://bumf76f7o48g2" path="res://assets/nature/stone_smallE.glb" id="56_7usa1"]
[ext_resource type="PackedScene" uid="uid://ch4wkhrj7degj" path="res://assets/nature/stone_tallA.glb" id="57_uql5m"]
[ext_resource type="PackedScene" uid="uid://drjaug8u3boqg" path="res://assets/nature/stone_tallB.glb" id="58_qpy2l"]
[ext_resource type="PackedScene" uid="uid://co3a4st6052mf" path="res://assets/nature/rock_largeA.glb" id="59_47uvh"]
[ext_resource type="PackedScene" uid="uid://be2so4joayf6h" path="res://assets/nature/rock_largeB.glb" id="60_h3iex"]
[ext_resource type="PackedScene" uid="uid://cnjlkb1vp1e8i" path="res://assets/nature/rock_smallI.glb" id="61_fv6dp"]
[ext_resource type="PackedScene" uid="uid://b8n2amn3xbk2d" path="res://assets/nature/rock_largeE.glb" id="62_fkodr"]
[ext_resource type="PackedScene" uid="uid://dwos0bxoj3254" path="res://assets/nature/rock_tallA.glb" id="63_t0hgs"]
[ext_resource type="PackedScene" uid="uid://lh8822xqcap8" path="res://assets/nature/rock_tallB.glb" id="64_jrafx"]
[ext_resource type="PackedScene" uid="uid://qb5xjbm05kur" path="res://assets/nature/rock_smallFlatC.glb" id="65_o24vq"]
[ext_resource type="PackedScene" uid="uid://bwr5o3srgo6ci" path="res://assets/nature/rock_smallTopA.glb" id="66_jyhum"]
[ext_resource type="PackedScene" uid="uid://dt2ysdtpwvrto" path="res://assets/nature/rock_smallTopB.glb" id="67_b4c6x"]
[ext_resource type="PackedScene" uid="uid://c4p0b3a5t73vf" path="res://assets/nature/plant_bush.glb" id="68_efa5t"]
[ext_resource type="PackedScene" uid="uid://bxwgysvw0j0as" path="res://assets/nature/plant_bushLarge.glb" id="69_qqd5g"]
[ext_resource type="PackedScene" uid="uid://dsdw0g4kgwgu3" path="res://assets/nature/plant_bushLargeTriangle.glb" id="70_pxdjd"]
[ext_resource type="PackedScene" uid="uid://bbjcilgxjxhuw" path="res://assets/nature/plant_bushSmall.glb" id="71_tpd41"]
[ext_resource type="PackedScene" uid="uid://qw76kxsd535q" path="res://assets/nature/crop_pumpkin.glb" id="72_copia"]
[ext_resource type="PackedScene" uid="uid://ba0xmgoui2ecq" path="res://assets/nature/crops_cornStageD.glb" id="73_fwjgo"]
[ext_resource type="PackedScene" uid="uid://c4485f0u0i380" path="res://assets/nature/log_stack.glb" id="74_4hvau"]
[ext_resource type="PackedScene" uid="uid://b6x6406nnbrna" path="res://assets/nature/log_stackLarge.glb" id="75_dkec4"]
[ext_resource type="PackedScene" uid="uid://b482vruam71b7" path="res://assets/nature/log.glb" id="76_6cjor"]
[ext_resource type="PackedScene" uid="uid://b1wgypd5uahlu" path="res://assets/nature/log_large.glb" id="77_jmnu3"]
[ext_resource type="PackedScene" uid="uid://didroud37jwg0" path="res://assets/nature/campfire_stones.glb" id="78_h553u"]
[ext_resource type="PackedScene" uid="uid://uyx1yabqlp6k" path="res://assets/nature/tent_detailedOpen.glb" id="79_olm36"]
[ext_resource type="PackedScene" uid="uid://d32uxiw6qwakt" path="res://assets/nature/campfire_logs.glb" id="80_uxtd5"]
[ext_resource type="PackedScene" uid="uid://i50skbowrrgd" path="res://assets/nature/bed_floor.glb" id="81_rliao"]
[ext_resource type="PackedScene" uid="uid://dta2xaeu1wp1m" path="res://assets/nature/rock_tallC.glb" id="82_5r3jb"]
[ext_resource type="PackedScene" uid="uid://cbu3o3mar08cj" path="res://assets/nature/rock_tallD.glb" id="83_4sqjt"]
[ext_resource type="PackedScene" uid="uid://8u48hl73tu0k" path="res://assets/nature/rock_tallE.glb" id="84_allsl"]
[ext_resource type="PackedScene" uid="uid://b0e7ir6jb6fdo" path="res://assets/nature/rock_tallF.glb" id="85_bagfi"]
[ext_resource type="PackedScene" uid="uid://d2xdtaywhytul" path="res://assets/nature/rock_tallG.glb" id="86_tnets"]
[ext_resource type="PackedScene" uid="uid://2c1ncbsgb0x2" path="res://assets/nature/rock_tallH.glb" id="87_2rbtg"]
[ext_resource type="PackedScene" uid="uid://df7ahcu56ehko" path="res://assets/nature/rock_smallE.glb" id="88_p2be1"]
[ext_resource type="PackedScene" uid="uid://brddiv7rmdf6h" path="res://assets/nature/fence_planks.glb" id="89_7bnen"]
[ext_resource type="PackedScene" uid="uid://bb1lyd11bactc" path="res://assets/nature/fence_simpleCenter.glb" id="90_2lmap"]
[ext_resource type="PackedScene" uid="uid://dj4b4ec4a86yt" path="res://assets/nature/fence_bendCenter.glb" id="91_2g4cr"]
[ext_resource type="Shader" uid="uid://dini6mo8emyqb" path="res://waterfall.tres" id="92_eexyd"]
[ext_resource type="Texture2D" uid="uid://bpug207fxqixc" path="res://assets/texture/glow.png" id="93_358ou"]
[ext_resource type="PackedScene" uid="uid://dmr0fcamx7t56" path="res://addons/virtual_joystick/virtual_joystick_scene.tscn" id="95_24n83"]
[ext_resource type="Script" uid="uid://cfouqj2eugbat" path="res://scripts/UI/jump_button.gd" id="96_nepnn"]
[ext_resource type="Texture2D" uid="uid://bm480142ue5ch" path="res://assets/images/global.png" id="96_vurqs"]
[ext_resource type="Texture2D" uid="uid://v7u0ts8h5rs4" path="res://assets/icons/ingame_ui/jump.png" id="97_fjbq0"]
[ext_resource type="Texture2D" uid="uid://dtn7pv4bmr81q" path="res://assets/images/local.png" id="97_qjith"]
[ext_resource type="FontFile" uid="uid://cdh63neq4ginm" path="res://addons/toastparty/fonts/Light.ttf" id="102_5lshx"]
[ext_resource type="Texture2D" uid="uid://bjvfvmuhdpwb" path="res://assets/images/send_global_message.png" id="103_vurqs"]
[ext_resource type="PackedScene" uid="uid://uy4saus24gv8" path="res://scenes/Utils/ocean.tscn" id="104_wcd50"]
[ext_resource type="PackedScene" uid="uid://cm26nto6fstcx" path="res://scenes/NewPlayer/Player.tscn" id="108_8hidn"]
[ext_resource type="Script" uid="uid://bmtfr28wun4gc" path="res://scripts/BombTag_game/spawn_points.gd" id="110_ao4ib"]
[ext_resource type="Shader" uid="uid://yj6bs51fsu4t" path="res://shaders/sky.gdshader" id="110_sn4s7"]

[sub_resource type="ProceduralSkyMaterial" id="ProceduralSkyMaterial_sn4s7"]
sky_top_color = Color(0.247742, 0.526487, 0.812359, 1)
sky_curve = 0.178381
ground_bottom_color = Color(0.247059, 0.52549, 0.811765, 1)
ground_curve = 0.0207053
energy_multiplier = 1.1

[sub_resource type="Sky" id="Sky_8hidn"]
sky_material = SubResource("ProceduralSkyMaterial_sn4s7")

[sub_resource type="Environment" id="Environment_qkeux"]
background_mode = 2
background_energy_multiplier = 1.5
sky = SubResource("Sky_8hidn")
ambient_light_color = Color(1, 1, 1, 1)
ambient_light_sky_contribution = 0.0
fog_light_color = Color(0.83627, 0.386967, 0.432367, 1)
fog_light_energy = 0.15
fog_density = 0.0335
fog_aerial_perspective = 0.129
adjustment_brightness = 0.53
adjustment_saturation = 0.44

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_c1tdp"]
default_input_values = [0, Quaternion(0, 0, 0, 0), 1, Quaternion(0, 0, 0, 0)]
op_type = 2
operator = 2

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_7jk00"]
default_input_values = [0, Quaternion(0, 0, 0, 0), 1, Quaternion(1.5, 1.25, 1.25, 1.25)]
op_type = 2
operator = 5

[sub_resource type="VisualShaderNodeFloatConstant" id="VisualShaderNodeFloatConstant_71hbl"]
constant = 1.0

[sub_resource type="VisualShaderNodeFloatConstant" id="VisualShaderNodeFloatConstant_haau4"]
constant = 1.0

[sub_resource type="VisualShaderNodeProximityFade" id="VisualShaderNodeProximityFade_qbva1"]

[sub_resource type="VisualShaderNodeColorParameter" id="VisualShaderNodeColorParameter_4l1m0"]
parameter_name = "ColorParameter"
default_value_enabled = true
default_value = Color(0.000136114, 0.495414, 0.951678, 1)

[sub_resource type="FastNoiseLite" id="FastNoiseLite_h8uke"]
noise_type = 2
seed = 1
fractal_type = 0
cellular_distance_function = 1

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_ktoxc"]
seamless = true
noise = SubResource("FastNoiseLite_h8uke")

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_if5wm"]
texture = SubResource("NoiseTexture2D_ktoxc")

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_moi75"]
output_port_for_preview = 0
default_input_values = [0, Quaternion(0, 0, 0, 0), 1, Quaternion(0, 0, 0, 0)]
op_type = 2

[sub_resource type="VisualShaderNodeUVFunc" id="VisualShaderNodeUVFunc_iiwgh"]
default_input_values = [1, Vector2(0.005, 0), 2, Vector2(0, 0)]

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_w6dgh"]
input_name = "time"

[sub_resource type="FastNoiseLite" id="FastNoiseLite_ddys4"]
noise_type = 2
seed = 3
fractal_type = 0
cellular_distance_function = 1

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_ej76u"]
width = 975
height = 1201
seamless = true
noise = SubResource("FastNoiseLite_ddys4")

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_cidxk"]
texture = SubResource("NoiseTexture2D_ej76u")

[sub_resource type="VisualShaderNodeUVFunc" id="VisualShaderNodeUVFunc_y5bqd"]
default_input_values = [1, Vector2(-0.06, 0), 2, Vector2(0, 0)]

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_ku7ll"]
input_name = "time"

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_2te3p"]
input_name = "time"

[sub_resource type="VisualShaderNodeUVFunc" id="VisualShaderNodeUVFunc_clqha"]
default_input_values = [1, Vector2(0.1, 0.1), 2, Vector2(0, 0)]

[sub_resource type="VisualShaderNodeTexture" id="VisualShaderNodeTexture_kgc80"]
texture = SubResource("NoiseTexture2D_ktoxc")

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_6jigg"]
input_name = "vertex"

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_uqse7"]
input_name = "normal"

[sub_resource type="VisualShaderNodeMultiplyAdd" id="VisualShaderNodeMultiplyAdd_mlsjd"]
default_input_values = [0, Vector4(0, 0, 0, 0), 1, Vector4(1, 1, 1, 1), 2, Vector4(0, 0, 0, 0)]
op_type = 3

[sub_resource type="VisualShaderNodeVectorOp" id="VisualShaderNodeVectorOp_4jdgv"]
default_input_values = [0, Vector3(0, 0, 0), 1, Vector3(0.01, 0.2, 0.25)]
operator = 2

[sub_resource type="VisualShader" id="VisualShader_b7270"]
code = "shader_type spatial;
render_mode blend_mix, depth_draw_opaque, cull_back, diffuse_lambert, specular_schlick_ggx;

uniform sampler2D tex_vtx_4;
uniform vec4 ColorParameter : source_color = vec4(0.000136, 0.495414, 0.951678, 1.000000);
uniform sampler2D tex_frg_3;
uniform sampler2D tex_frg_7;



void vertex() {
// Input:2
	float n_out2p0 = TIME;


// UVFunc:3
	vec2 n_in3p1 = vec2(0.10000, 0.10000);
	vec2 n_out3p0 = vec2(n_out2p0) * n_in3p1 + UV;


// Texture2D:4
	vec4 n_out4p0 = texture(tex_vtx_4, n_out3p0);


// Input:6
	vec3 n_out6p0 = NORMAL;


// VectorOp:8
	vec3 n_in8p1 = vec3(0.01000, 0.20000, 0.25000);
	vec3 n_out8p0 = n_out6p0 * n_in8p1;


// Input:5
	vec3 n_out5p0 = VERTEX;


// MultiplyAdd:7
	vec4 n_out7p0 = (n_out4p0 * vec4(n_out8p0, 0.0)) + vec4(n_out5p0, 0.0);


// Output:0
	VERTEX = vec3(n_out7p0.xyz);


}

void fragment() {
// ColorParameter:2
	vec4 n_out2p0 = ColorParameter;


// Input:6
	float n_out6p0 = TIME;


// UVFunc:5
	vec2 n_in5p1 = vec2(0.00500, 0.00000);
	vec2 n_out5p0 = vec2(n_out6p0) * n_in5p1 + UV;


// Texture2D:3
	vec4 n_out3p0 = texture(tex_frg_3, n_out5p0);


// Input:9
	float n_out9p0 = TIME;


// UVFunc:8
	vec2 n_in8p1 = vec2(-0.06000, 0.00000);
	vec2 n_out8p0 = vec2(n_out9p0) * n_in8p1 + UV;


// Texture2D:7
	vec4 n_out7p0 = texture(tex_frg_7, n_out8p0);


// VectorOp:10
	vec4 n_out10p0 = n_out3p0 * n_out7p0;


// VectorOp:11
	vec4 n_in11p1 = vec4(1.50000, 1.25000, 1.25000, 1.25000);
	vec4 n_out11p0 = pow(n_out10p0, n_in11p1);


// VectorOp:4
	vec4 n_out4p0 = n_out2p0 + n_out11p0;


// FloatConstant:12
	float n_out12p0 = 1.000000;


// FloatConstant:13
	float n_out13p0 = 1.000000;


// Output:0
	ALBEDO = vec3(n_out4p0.xyz);
	ALPHA = n_out12p0;
	ROUGHNESS = n_out13p0;
	EMISSION = vec3(n_out11p0.xyz);


}
"
nodes/vertex/2/node = SubResource("VisualShaderNodeInput_2te3p")
nodes/vertex/2/position = Vector2(-780, 220)
nodes/vertex/3/node = SubResource("VisualShaderNodeUVFunc_clqha")
nodes/vertex/3/position = Vector2(-360, 220)
nodes/vertex/4/node = SubResource("VisualShaderNodeTexture_kgc80")
nodes/vertex/4/position = Vector2(-80, 240)
nodes/vertex/5/node = SubResource("VisualShaderNodeInput_6jigg")
nodes/vertex/5/position = Vector2(-780, 740)
nodes/vertex/6/node = SubResource("VisualShaderNodeInput_uqse7")
nodes/vertex/6/position = Vector2(-760, 480)
nodes/vertex/7/node = SubResource("VisualShaderNodeMultiplyAdd_mlsjd")
nodes/vertex/7/position = Vector2(160, 660)
nodes/vertex/8/node = SubResource("VisualShaderNodeVectorOp_4jdgv")
nodes/vertex/8/position = Vector2(-400, 600)
nodes/vertex/connections = PackedInt32Array(2, 0, 3, 2, 3, 0, 4, 0, 4, 0, 7, 0, 5, 0, 7, 2, 6, 0, 8, 0, 8, 0, 7, 1, 7, 0, 0, 0)
nodes/fragment/2/node = SubResource("VisualShaderNodeColorParameter_4l1m0")
nodes/fragment/2/position = Vector2(-580, -220)
nodes/fragment/3/node = SubResource("VisualShaderNodeTexture_if5wm")
nodes/fragment/3/position = Vector2(-1420, -280)
nodes/fragment/4/node = SubResource("VisualShaderNodeVectorOp_moi75")
nodes/fragment/4/position = Vector2(-20, -80)
nodes/fragment/5/node = SubResource("VisualShaderNodeUVFunc_iiwgh")
nodes/fragment/5/position = Vector2(-1820, -280)
nodes/fragment/6/node = SubResource("VisualShaderNodeInput_w6dgh")
nodes/fragment/6/position = Vector2(-2300, -200)
nodes/fragment/7/node = SubResource("VisualShaderNodeTexture_cidxk")
nodes/fragment/7/position = Vector2(-1420, 120)
nodes/fragment/8/node = SubResource("VisualShaderNodeUVFunc_y5bqd")
nodes/fragment/8/position = Vector2(-1820, 120)
nodes/fragment/9/node = SubResource("VisualShaderNodeInput_ku7ll")
nodes/fragment/9/position = Vector2(-2300, 200)
nodes/fragment/10/node = SubResource("VisualShaderNodeVectorOp_c1tdp")
nodes/fragment/10/position = Vector2(-840, 20)
nodes/fragment/11/node = SubResource("VisualShaderNodeVectorOp_7jk00")
nodes/fragment/11/position = Vector2(-560, 80)
nodes/fragment/12/node = SubResource("VisualShaderNodeFloatConstant_71hbl")
nodes/fragment/12/position = Vector2(-100, 400)
nodes/fragment/13/node = SubResource("VisualShaderNodeFloatConstant_haau4")
nodes/fragment/13/position = Vector2(-100, 500)
nodes/fragment/14/node = SubResource("VisualShaderNodeProximityFade_qbva1")
nodes/fragment/14/position = Vector2(-481.655, -356.03)
nodes/fragment/connections = PackedInt32Array(5, 0, 3, 0, 6, 0, 5, 2, 8, 0, 7, 0, 9, 0, 8, 2, 3, 0, 10, 0, 7, 0, 10, 1, 10, 0, 11, 0, 2, 0, 4, 0, 11, 0, 4, 1, 13, 0, 0, 3, 12, 0, 0, 1, 11, 0, 0, 5, 4, 0, 0, 0)

[sub_resource type="ShaderMaterial" id="ShaderMaterial_0sbtv"]
render_priority = 0
shader = SubResource("VisualShader_b7270")
shader_parameter/ColorParameter = Color(0.000136, 0.495414, 0.951678, 1)

[sub_resource type="PlaneMesh" id="PlaneMesh_28ggn"]
material = SubResource("ShaderMaterial_0sbtv")
size = Vector2(10, 10)
subdivide_width = 20
subdivide_depth = 20

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_7rklp"]
resource_name = "dirt"
albedo_color = Color(0.948242, 0.744326, 0.619001, 1)
metallic = 1.0

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_3tdbn"]
resource_name = "grass"
albedo_color = Color(0.45234, 0.929502, 0.865877, 1)
metallic = 1.0

[sub_resource type="ArrayMesh" id="ArrayMesh_5wvxb"]
_surfaces = [{
"aabb": AABB(-0.5, 0, -0.5, 1, 1, 1),
"format": ***********,
"index_count": 252,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYABwAIAAYACAAAAAYAAAAJAAYACQAKAAYACwAJAAAAAQAAAAgADAAIAAcADQAHAAUADwAOAAcABwANAA8AEgAQABEAEQATABIAFAAIAAwADAAVABQAFwAWABIAEgATABcAGQAYAA4ADgAPABkAHAAaABsAGwAdABwAHgAYABkAGQAfAB4AIQAgAAQABAAGACEAIwAiAAIAAgADACMAEQAQAB4AHgAfABEAJAAWABcAFwAlACQAKAAmACcAJwApACgAJgAJAAsACwAnACYAKgAoACkAKQArACoALgAsAC0ALQAvAC4ABQAEACAAIAAwAAUAMQAsAC4ALgAyADEAMwAiACMAIwA0ADMANQAwACAAIAAhADUAOAA2ADcANwA5ADgAMwA0ADoAOgA7ADMACgA8AAYAIQAGADwAPAA1ACEAPAA9ADUAPQA5ADUAOQAcADUAHAAdADUANwAcADkAPgA5AD0APwA9ADwAQAAkACUAJQBBAEAAQwBCAD4APgA9AEMAMQAyAEQARABFADEARQBEADYANgA4AEUALQBGAEcARwAvAC0AKgArAD8APwA8ACoASABAAEEAQQBJAEgARwBGABQAFAAVAEcAOwA6AEIAQgBDADsAGgBIAEkASQAbABoA"),
"name": "dirt",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 74,
"vertex_data": PackedByteArray("//9mZmZmAAD//5mZZmYAAFTVZmbvdwAAVNWZme93AAD//wAA//8AAP///////wAA//8AAAAAAAD//8zMzMwAAP//mZmZmQAA//8zMzIzAAD//zMzAAAAAP//ZmYyMwAA///MzJmZAAD/////zMwAAFTVzMxV3gAAVNX//1XeAABUVczMzMwAAFRV///MzAAAqirMzFXeAACqKv//Vd4AAFTVmZkiqwAAVNXMzCKrAACpIczMVNUAAKkh//9U1QAAqqrMzMzMAACqqv//zMwAAKkhzMyqKgAAqSH//6oqAAAyM8zMAAAAADIz//8AAAAA/3/MzFXeAAD/f///Vd4AAAAAAAD//wAAAAAAAAAAAACqqmZmZmYAAKqqmZlmZgAAMjPMzKqqAAAyM///qqoAAFTVMzO8RAAAVNVmZrxEAADawjMzJD0AANrCZmYkPQAAQrszM6oqAABCu2ZmqioAAG1hmZmRngAA/3+ZmSKrAABtYczMkZ4AAP9/zMwiqwAAAAD/////AADcVJmZ/38AANxUzMz/fwAAsI1mZk5yAACwjZmZTnIAAAAA//8AAAAA3FTMzKoqAABmZszMAAAAANxUmZmqKgAAZmaZmQAAAACZmZmZVFUAAJmZZmZUVQAAzMwzMwAAAACZmWZmAAAAAJmZmZkAAAAAzMxmZgAAAACpIczM/38AAKkh////fwAAD4iZmaoqAAAPiGZmqioAAGZmzMxUVQAAZmaZmVRVAACqqpmZmZkAAKqqzMyZmQAAMjPMzFRVAAAyM///VFUAAA==")
}, {
"aabb": AABB(-0.5, 0.2, -0.5, 1, 0.8, 1),
"format": ***********,
"index_count": 387,
"index_data": PackedByteArray("AgAAAAEAAwACAAEABAADAAEABQAEAAEAAQAGAAUAAgADAAcAAgAHAAgAAgAIAAkACQAKAAIABwALAAgACwAMAAgACwANAAwADQAOAAwADQAPAA4ADwAQAA4ADwARABAAEQASABAAEQATABIAFAAHAAMADQALABUAFQAWAA0AGQAXABgAGAAaABkAHQAbABwAHAAeAB0AIQAfACAAIAAiACEAGwAdACMAJgAkACUAJQAnACYAKgAoACkAKwAqACkALAAqACsAKwAfACwAHwAhACwAIQAtACwAIQAuAC0ALwArACkAMAAvACkAKQAxADAANAAyADMAMwA1ADQAMgA2ADMAMwA3ADUANwA4ADUAOQAkACYAJgA6ADkAPQA7ADwAPAA+AD0APAA/AD4APwBAAD4AAwBBAEIAQgAUAAMAQgBDABQAQwBEABQARQBCAEEANQA4AEYARgBHADUASgBIAEkASQBLAEoAIQAiAEwATABNACEALgAhAE0ATgAuAE0ATQBPAE4AUgBQAFEAUQBTAFIAVAAeABwAHABVAFQAFgBWAA8ADwANABYAHwArAFcAVwAgAB8AWAAlACQAJABZAFgAWQAkAEgASAAjAFkAIwAdAFkAHQBaAFkAHQBUAFoAHQAeAFQAVAA0AFoANAA1AFoANQBHAFoASgAjAEgAOQBIACQAEQAPAFYAVgBbABEAUgAYABcAFwBQAFIAVwArAC8ALwBcAFcAIwBKAF0AXQAbACMAXQBeABsASgBfAF0ASgBLAF8AQQADAAQABABgAEEACwAHAGEAYQAVAAsAZABiAGMAYwBlAGQAEQBbAGYAZgATABEAaQBnAGgAaABqAGkAYgBqAGgAawBiAGgAbABrAGgAaABtAGwAagBiABoAGgBuAGoAGgAYAG4AGABvAG4AGABSAG8AUgBwAG8AUgBTAHAAZAAaAGIAVQAyADQANABUAFUAYQAHABQAFABEAGEAGQAaAGQAZABlABkAYwBiAGsAawBxAGMASQBIADkAOQA6AEkA"),
"name": "grass",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 114,
"vertex_data": PackedByteArray("qSH/v/9/AAAyM/+/VFUAADIz/7+qqgAA3FT/v6oqAABmZv+/AAAAADIz/78AAAAAqSH/v6oqAADcVP+//38AAFRV/7/MzAAAqir/v1XeAACpIf+/VNUAAG1h/7+RngAA/3//v1XeAAD/f/+/IqsAAKqq/7/MzAAAqqr/v5mZAABU1f+/Vd4AAFTV/78iqwAA////v8zMAAD///+/mZkAAGZm/79UVQAAf2r/r3+VAAD/f/+vVZ4AAKqq/2+ZWQAAqqr/f2ZmAABro/9vk1wAALCN/39OcgAAHDH/7+LOAABUVf/v/78AAKoq//9V3gAAVFX//8zMAADawv8/JD0AAO3L/y8RNAAAVNX/P7xEAABU1f8v7zcAAKkh//9U1QAAqSH//6oqAAAyM///AAAAAHYu/++qKgAA/z//7wAAAACwjf8/TnIAAJmZ/z9UVQAAqqr/P2ZmAABCu/8/qioAAFTV/z/vdwAA////P2ZmAAD///8/MjMAAMzM/z8AAAAAmZn/PwAAAAAPiP8/qioAAKqq/+//vwAA/7//9yrPAACqqv//zMwAAFTV//9V3gAAVLX/72HEAACpyv/vJs0AAFTV/++I0QAAMjP//1RVAAD/P//vVFUAAEK7AACqKgAAzMwAAAAAAADawgAAJD0AAFTVAAC8RAAA//8AAAAAAAD//wAAMjMAAKlh/6+qKgAAB2T/t/8/AADQbv+vqkoAADJz/69UVQAAC2b/r1U1AAD////v/78AAP/////MzAAAqSH///9/AAB2Lv/v/38AADIz//+qqgAA/z//76qqAAD/3/8vjTMAAKnq/zeRNQAA////L2YmAABU9f8vyCoAAFTV/28iawAA////b5lZAABU1f9/73cAAP///39mZgAA/3///1XeAAD/f//viNEAAKqq/6/MjAAAD8j/L6oqAAAAAP//AAAAAAAA/////wAA////////AABU1f+vVZ4AAJjZ/y8AAAAA1DD/9/+/AADYMv/vqcoAAJ07/+9UtQAAMnP/rwAAAACpYf+v/38AAA+I/3+qKgAA3JT/b6oqAACZmf9/VFUAAGWm/29UVQAA////r8yMAADcVP9//38AAGZm/39UVQAAbWH/f5GeAAD/f/9/IqsAAJmZ/38AAAAAZmb/fwAAAADcVP9/qioAAKqq/3+ZmQAAVNX/fyKrAAD///9/mZkAAGWm/28AAAAA")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_ou0lw"]
resource_name = "cliff_stepsCornerInner_rock_Mesh cliff_stepsCornerInner_rock"
_surfaces = [{
"aabb": AABB(-0.5, 0, -0.5, 1, 1, 1),
"attribute_data": PackedByteArray("4RDMTuEQiDTjOMxO4ziINPwQVYP8EAAAAu9Vg2Q9RBrLaYg0M5bMTprCEWkC7xFpmsLMTjOWiDTLaUQaZD0AAKVaRBqkMkQapVoAAKQyAACLu0Qai7sAAI3jRBqN4wAAwiGINMIhRBrESYg0xElEGoSFRBp6ekQahIUAAHp6AABzREQacRxEGnNEAABxHAAAWaVEGlmlAABazUQaWs0AABd3RBoXdwAAGZ9EGhmfAAAC7wDF/BAAxQLvqkH8EKpBNmbMTjQ+zE42Zog0ND6INOeIRBrlYEQa54gAAOVgAABxHEQacRwAAHNERBpzRAAAFU8RaRVPzE5oYBFpaGDMTgAAEWkAAMxOASgRaQEozE6WnxFplp/MTumwEWnpsMxOcY6INMZxiDRxjkQaxnFEGgLvVYP8EFWDAu8AAPwQAACNcYg0jXFEGjiOiDQ4jkQaVFXMTlRViDSAcMxOgHCINALvAAAC71WD/BAAAPwQVYM6tkQaPN5EGjq2iDQ83og0fo+INKqqiDR+j8xOqqrMTmQ9EWn8EFWD/BARaQLvVYMC7wAAy2nMTjOWiDSawkQamsIAADOWRBrLaYg0ZD3MThd3RBoXdwAAGZ9EGhmfAAAbx4g0He+INBvHzE4d78xOxnFEGsiZRBrGcYg0yJmINKqqRBqr0kQaqqqINKvSiDQ2Zog0NmZEGjiOiDQ4jkQa/dfMTv//zE791xFp//8RaeVgRBrlYAAA54hEGueIAABUVYg0Uy2INFRVRBpTLUQayJmINMrBiDTImcxOysHMTou7RBqLuwAAjeNEGo3jAAA="),
"format": ***********,
"index_count": 252,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYABwAIAAYACAAJAAYACQAKAAYACgALAAYADAAKAAkADQAJAAgADgAIAAcADwAHAAUAEgAQABEAEQATABIAFgAUABUAFQAXABYAGgAYABkAGQAbABoAHgAcAB0AHQAfAB4AIgAgACEAIQAjACIAJgAkACUAJQAnACYAKgAoACkAKQArACoALgAsAC0ALQAvAC4AMgAwADEAMQAzADIANgA0ADUANQA3ADYAOgA4ADkAOQA7ADoAPgA8AD0APQA/AD4AQgBAAEEAQQBDAEIARgBEAEUARQBHAEYASgBIAEkASQBLAEoATgBMAE0ATQBPAE4AUgBQAFEAUQBTAFIAVgBUAFUAVQBXAFYAWgBYAFkAWQBbAFoAXgBcAF0AXQBfAF4AYgBgAGEAYQBjAGIAZgBkAGUAZwBlAGQAZABoAGcAZABpAGgAaQBqAGgAagBrAGgAawBsAGgAbQBrAGoAbgBqAGkAbwBpAGQAcgBwAHEAcQBzAHIAdgB0AHUAdQB3AHYAegB4AHkAeQB7AHoAfgB8AH0AfQB/AH4AggCAAIEAgQCDAIIAhgCEAIUAhQCHAIYAigCIAIkAiQCLAIoAjgCMAI0AjQCPAI4AkgCQAJEAkQCTAJIAlgCUAJUAlQCXAJYA"),
"material": SubResource("StandardMaterial3D_7rklp"),
"name": "dirt",
"primitive": 3,
"uv_scale": Vector4(45.3942, 76.7402, 0, 0),
"vertex_count": 152,
"vertex_data": PackedByteArray("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")
}, {
"aabb": AABB(-0.5, 0.2, -0.5, 1, 0.8, 1),
"attribute_data": PackedByteArray("yzAqg4Q/YG6EP/SXx1uWWYFqzUSEP81EyzCWWcdbKoMsXJWoWjghscswvqxUZg+S/38hsf9/LpjSo5Wo0qOjj6THIbGkxy6Yd+uVqHfro4+BamBu+422LNSJNTU6crYsUHY1NZtSMFexVrFOL1kwV/9wsU5v1vUdjrX1HV/gdhWkuXYVamFne0Rd5oOlUGd7u1Tmg6d6/ANXhfwD/3/FDSekAADiygAAPah+CPjOfgh+i358fZVgbtKjsXbBsZZZpMc8fyG4l2Kkx0pmd+uxdnfrv116wM1EfZXNRMSGlllwSvUdBzW2GVpGdhWfH3YVwUD1HWMt9R21I/UdX+B2FUnc9R2kuXYVjrX1HcGxlll6wM1EIbiXYqTHSmZ3681Ed+u/XfLL8D+furE7CNBxN02pcTfmrvA/N6XwP0PC8D/BV34IBjF+CNdbAAAcNQAAG567CgWaOhNgd7sKSnM6E7omb24MHW9u0CrxZWgVMGoVBPFlAABvbq4Jb24SN3RMVxB0TCg79kNtFPZDnoi7CrSMOhPjYbsK+WU6E/NiNTUJZ7Ysrok1NcSNtixZr2d7Q6vmg5SeZ3u6ouaDhD/NRMswllmHFM1EhxSIwcswKoPLML6sWjghsXfriMH/fyGxLFyVqNKjlaikxyGxd+uVqIQ/9JeEP2BusVZxN8da8D/2L3E3DDTwPwlnbFkea+thTkBsWWNE62Eu1fFl6fvxZUTZb27//29uWkZ2FQc1thmfH3YVjyn1HWMt9R3BQPUdcEr1HX+0+yE62/shlbh5KlDfeSrEjbYsrok1NQNytiwqdjU1sL9sWZu762H1mGxZ4JTrYWlHeSquIHkqf0v7IcQk+yHHWyqDgWpgblRmD5L/fy6YxIaWWX2VzUSBas1Ex1uWWX6LfnzSo6OP0qOxdqTHLpikxzx/d+ujj3frsXZ9lWBuSnM6E2B3uwoFmjoTG567Cjpytiz1mLYsUHY1NQudNTX/jrFOTamxTs+mMFdjrTBX1sT2Q5Hr9kPsyHRMp+90TONhuwqeiLsK+WU6E7SMOhM="),
"format": ***********,
"index_count": 387,
"index_data": PackedByteArray("AgAAAAEAAwACAAEABAADAAEABQAEAAEAAQAGAAUAAgADAAcAAgAHAAgAAgAIAAkACQAKAAIABwALAAgACwAMAAgACwANAAwADQAOAAwADQAPAA4ADwAQAA4ADwARABAAEQASABAAEQATABIAFAAHAAMAFwAVABYAFgAYABcAGwAZABoAGgAcABsAHwAdAB4AHgAgAB8AIwAhACIAIgAkACMAJwAlACYAKgAoACkAKQArACoALgAsAC0ALwAuAC0AMAAuAC8ALwAxADAAMQAyADAAMgAzADAAMgA0ADMANQAvAC0ANgA1AC0ALQA3ADYAOgA4ADkAOQA7ADoAOAA8ADkAOQA9ADsAPQA+ADsAQQA/AEAAQABCAEEARQBDAEQARABGAEUARABHAEYARwBIAEYASwBJAEoASgBMAEsASgBNAEwATQBOAEwATwBKAEkAUgBQAFEAUQBTAFIAVgBUAFUAVQBXAFYAWgBYAFkAWQBbAFoAXABaAFsAXQBcAFsAWwBeAF0AYQBfAGAAYABiAGEAZQBjAGQAZABmAGUAaQBnAGgAaABqAGkAbQBrAGwAbABuAG0AcQBvAHAAcAByAHEAcgBwAHMAcwB0AHIAdAB1AHIAdQB2AHIAdQB3AHYAdQB4AHcAdwB5AHYAeQB6AHYAegB7AHYAfAB0AHMAfQBzAHAAgAB+AH8AfwCBAIAAhACCAIMAgwCFAIQAiACGAIcAhwCJAIgAjACKAIsAiwCNAIwAiwCOAI0AigCPAIsAigCQAI8AkwCRAJIAkgCUAJMAlwCVAJYAlgCYAJcAmwCZAJoAmgCcAJsAnwCdAJ4AngCgAJ8AowChAKIAogCkAKMApQCkAKIApgClAKIApwCmAKIAogCoAKcApAClAKkAqQCqAKQAqQCrAKoAqwCsAKoAqwCtAKwArQCuAKwArQCvAK4AsACpAKUAswCxALIAsgC0ALMAtwC1ALYAtgC4ALcAuwC5ALoAugC8ALsAvwC9AL4AvgDAAL8AwwDBAMIAwgDEAMMA"),
"material": SubResource("StandardMaterial3D_3tdbn"),
"name": "grass",
"primitive": 3,
"uv_scale": Vector4(46.8909, 80.8031, 0, 0),
"vertex_count": 197,
"vertex_data": PackedByteArray("qSH/v/9//78yM/+/VFX/vzIz/7+qqv+/3FT/v6oq/79mZv+/AAD/vzIz/78AAP+/qSH/v6oq/7/cVP+//3//v1RV/7/MzP+/qir/v1Xe/7+pIf+/VNX/v21h/7+Rnv+//3//v1Xe/7//f/+/Iqv/v6qq/7/MzP+/qqr/v5mZ/79U1f+/Vd7/v1TV/78iq/+/////v8zM/7////+/mZn/v2Zm/79UVf+/bWH/v5GeNvF/av+vf5U28f9//78iqzbx/3//r1WeNvGqqv9vmVk28aqq/39mZjbxa6P/b5NcNvGwjf9/TnI28Rwx/+/izjbxVFX/7/+/NvGqKv//Vd428VRV///MzDbx2sL/PyQ9NvHty/8vETQ28VTV/z+8RDbxVNX/L+83NvGqKv//Vd4v46kh//9U1S/jHDH/7+LOL+OpIf//qirutzIz//8AAO63di7/76oq7rf/P//vAADut7CN/z9Ocv+/mZn/P1RV/7+qqv8/Zmb/v0K7/z+qKv+/VNX/P+93/7/awv8/JD3/v1TV/z+8RP+/////P2Zm/7////8/MjP/v8zM/z8AAP+/mZn/PwAA/78PiP8/qir/v6qq/+//vzbx/7//9yrPNvGqqv//zMw28VTV//9V3jbxVLX/72HENvGpyv/vJs028VTV/++I0TbxqSH//6oqltN2Lv/vqiqW0zIz//9UVZbT/z//71RVltNCuwAAqir/v8zMAAAAAP+/2sIAACQ9/79U1QAAvET/v///AAAAAP+///8AADIz/7+pYf+vqiqW0wdk/7f/P5bT3FT/v6oqltNmZv+/VFWW09Bu/6+qSpbTMnP/r1RVltMLZv+vVTWW01TV/++I0Tbx////7/+/NvFU1f//Vd428f/////MzDbxqSH///9/ltN2Lv/v/3+W0zIz//+qqpbT/z//76qqltNU1f8v7zc28f/f/y+NMzbxVNX/P7xENvGp6v83kTU28f///z8yMzbx////L2YmNvFU9f8vyCo28VTV/28iazbx////b5lZNvFU1f9/73c28f///39mZjbxVFX//8zMNvFUVf/v/7828f9///9V3jbx/3//74jRNvGqqv+vzIw28aqq/7+ZmTbx/3//r1WeNvH/f/+/Iqs28UK7/z+qKpbTD8j/L6oqltPawv8/JD2W0+3L/y8RNJbTMjP//wAA/7+pIf//qir/vwAA//8AAP+/AAD//////7+pIf///3//v6kh//9U1f+/qir//1Xe/7//////////v/9///9V3v+/VFX//8zM/7+qqv//zMz/v1TV//9V3v+//////8zM/78yM///qqr/vzIz//9UVf+/qqr/v5mZNvGqqv+vzIw28VTV/78iqzbxVNX/r1WeNvGqqv9/ZmY28aqq/2+ZWTbxVNX/f+93NvFU1f9vIms28UK7/z+qKu63zMz/PwAA7rcPyP8vqirut5jZ/y8AAO63MjP//6qq7rfUMP/3/7/ut6kh//9U1e63HDH/7+LO7rfYMv/vqcrut507/+9Ute63/z//76qq7rfcVP+/qirut2Zm/78AAO63qWH/r6oq7rcyc/+vAADut9xU/7//f5bTqWH/r/9/ltNtYf+/kZ6W039q/69/lZbTD4j/f6oqltPclP9vqiqW05mZ/39UVZbTZab/b1RVltNU1f+vVZ428f///6/MjDbxVNX/vyKrNvH///+/mZk28dxU/3//f/+/Zmb/f1RV/79tYf9/kZ7/v/9//38iq/+/D4j/f6oq/7+Zmf9/AAD/v2Zm/38AAP+/3FT/f6oq/7+wjf9/TnL/v6qq/3+Zmf+/qqr/f2Zm/79U1f9/Iqv/v1TV/3/vd/+/////f5mZ/7////9/Zmb/v5mZ/39UVf+/qqr/7/+/NvGqqv//zMw28f9//++I0Tbx/3///1XeNvHcVP+//3/ut2Zm/79UVe63qWH/r/9/7rcyc/+vVFXut7CN/39Ocu63mZn/f1RV7rdro/9vk1zut2Wm/29UVe63D4j/f6oq7reZmf9/AADut9yU/2+qKu63Zab/bwAA7repIf///3/utzIz//9UVe63di7/7/9/7rf/P//vVFXut////3////9/////f////3////9/////f/7//3////9/////f////3/+//9/////f////3////9/////f////3////9/////f////3////9/////f9uovAbbqLwG26i8BtuovAa8hiPXvIYj17yGI9e8hiPXvIYj17yGI9e8hiPXvIYj19uovAbbqLwG26i8BtuovAYns/sOJ7P7Diez+w42v9klNr/ZJTa/2SU2v9kl////f////3////9/////f////3/+//9/////f////3////9/////f////3/+//9/26i8BtuovAbbqLwG26i8BtuovAbbqLwG26i8BpuyThSbsk4Um7JOFJuyThT+//9/////f/7//3////9/////f/7//3+bsk4Um7JOFJuyThSbsk4Um7JOFJuyThSbsk4UvIYj17yGI9e8hiPXvIYj15uyThSbsk4Um7JOFJuyThS8hiPXvIYj17yGI9e8hiPXvIYj17yGI9e8hiPXvIYj17yGI9e8hiPXvIYj19uovAbbqLwG26i8BtuovAa8hiPXvIYj17yGI9e8hiPXm7JOFJuyThSbsk4Um7JOFP///3////9/////f////3////9/////f////3////9/////f////3////9//v//f/7//3////9/////f9uovAbbqLwG26i8BtuovAbbqLwG26i8BtuovAbbqLwGNr/ZJTa/2SU2v9klNr/ZJTa/2SU2v9klNr/ZJTa/2SU2v9klNr/ZJTa/2SU2v9klNr/ZJTa/2SU2v9klm7JOFJuyThSbsk4Um7JOFJuyThSbsk4Um7JOFJuyThS8hiPXvIYj17yGI9e8hiPX////f////3/+//9/////f////3////9/////f/7//3////9/////f////3////9/////f////3////9/////f7yGI9e8hiPXvIYj17yGI9c2v9klNr/ZJTa/2SU2v9klNr/ZJTa/2SU2v9klNr/ZJTa/2SU2v9klNr/ZJTa/2SU2v9klNr/ZJTa/2SU2v9kl")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_5wvxb")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_2r06b"]
data = PackedVector3Array(0.3333, 0.4, -0.0315, 0.5, 0.4, -0.1, 0.5, 0.6, -0.1, 0.5, 0.6, -0.1, 0.3333, 0.6, -0.0315, 0.3333, 0.4, -0.0315, 0.5, 0, -0.5, 0.5, 0, 0.5, 0.5, 1, 0.5, 0.5, 1, 0.5, 0.5, 0.8, 0.3, 0.5, 0, -0.5, 0.5, 0.8, 0.3, 0.5, 0.6, 0.1, 0.5, 0, -0.5, 0.5, 0.6, 0.1, 0.5, 0.4, -0.1, 0.5, 0, -0.5, 0.5, 0.4, -0.1, 0.5, 0.2, -0.3, 0.5, 0, -0.5, 0.5, 0.2, -0.3, 0.5, 0.2, -0.5, 0.5, 0, -0.5, 0.5, 0.4, -0.3, 0.5, 0.2, -0.3, 0.5, 0.4, -0.1, 0.5, 0.6, -0.1, 0.5, 0.4, -0.1, 0.5, 0.6, 0.1, 0.5, 0.8, 0.1, 0.5, 0.6, 0.1, 0.5, 0.8, 0.3, 0.5, 1, 0.3, 0.5, 0.8, 0.3, 0.5, 1, 0.5, 0.3333, 1, 0.3685, 0.3333, 0.8, 0.3685, 0.5, 0.8, 0.3, 0.5, 0.8, 0.3, 0.5, 1, 0.3, 0.3333, 1, 0.3685, -0.3333, 0.8, 0.3685, -0.1667, 0.8, 0.3, -0.1667, 1, 0.3, -0.1667, 1, 0.3, -0.3333, 1, 0.3685, -0.3333, 0.8, 0.3685, 0.3333, 0.6, 0.1685, 0.5, 0.6, 0.1, 0.5, 0.8, 0.1, 0.5, 0.8, 0.1, 0.3333, 0.8, 0.1685, 0.3333, 0.6, 0.1685, -0.3685, 1, 0.3333, -0.3685, 0.8, 0.3333, -0.3333, 0.8, 0.3685, -0.3333, 0.8, 0.3685, -0.3333, 1, 0.3685, -0.3685, 1, 0.3333, 0.1667, 1, 0.3, 0.1667, 0.8, 0.3, 0.3333, 0.8, 0.3685, 0.3333, 0.8, 0.3685, 0.3333, 1, 0.3685, 0.1667, 1, 0.3, -0.3, 0.8, -0.5, -0.3685, 0.8, -0.3333, -0.3685, 1, -0.3333, -0.3685, 1, -0.3333, -0.3, 1, -0.5, -0.3, 0.8, -0.5, 0, 0.8, 0.3685, 0.1667, 0.8, 0.3, 0.1667, 1, 0.3, 0.1667, 1, 0.3, 0, 1, 0.3685, 0, 0.8, 0.3685, -0.5, 0, -0.5, -0.5, 0, 0.5, 0.5, 0, 0.5, 0.5, 0, 0.5, 0.5, 0, -0.5, -0.5, 0, -0.5, 0.1667, 0.6, -0.1, 0.1667, 0.4, -0.1, 0.3333, 0.4, -0.0315, 0.3333, 0.4, -0.0315, 0.3333, 0.6, -0.0315, 0.1667, 0.6, -0.1, -0.1667, 1, 0.3, -0.1667, 0.8, 0.3, 0, 0.8, 0.3685, 0, 0.8, 0.3685, 0, 1, 0.3685, -0.1667, 1, 0.3, -0.3, 0.8, 0.1667, -0.3685, 0.8, 0.3333, -0.3685, 1, 0.3333, -0.3685, 1, 0.3333, -0.3, 1, 0.1667, -0.3, 0.8, 0.1667, 0.2612, 0.2, -0.2612, 0.3333, 0.2, -0.2315, 0.3333, 0.4, -0.2315, 0.3333, 0.4, -0.2315, 0.2612, 0.4, -0.2612, 0.2612, 0.2, -0.2612, 0.3333, 0.2, -0.2315, 0.5, 0.2, -0.3, 0.5, 0.4, -0.3, 0.5, 0.4, -0.3, 0.3333, 0.4, -0.2315, 0.3333, 0.2, -0.2315, 0.2315, 0.2, -0.3333, 0.2612, 0.2, -0.2612, 0.2612, 0.4, -0.2612, 0.2612, 0.4, -0.2612, 0.2315, 0.4, -0.3333, 0.2315, 0.2, -0.3333, -0.1194, 0.8, 0.1194, -0.1194, 0.6, 0.1194, 0, 0.6, 0.1685, 0, 0.6, 0.1685, 0, 0.8, 0.1685, -0.1194, 0.8, 0.1194, 0.5, 1, 0.5, 0.5, 0, 0.5, -0.5, 0, 0.5, -0.5, 0, 0.5, -0.5, 1, 0.5, 0.5, 1, 0.5, -0.1685, 0.6, 0, -0.1194, 0.6, 0.1194, -0.1194, 0.8, 0.1194, -0.1194, 0.8, 0.1194, -0.1685, 0.8, 0, -0.1685, 0.6, 0, 0.0535, 0.4, -0.0535, 0.1667, 0.4, -0.1, 0.1667, 0.6, -0.1, 0.1667, 0.6, -0.1, 0.0535, 0.6, -0.0535, 0.0535, 0.4, -0.0535, -0.5, 1, -0.5, -0.5, 1, 0.5, -0.5, 0, 0.5, -0.5, 0, 0.5, -0.5, 0, -0.5, -0.5, 1, -0.5, -0.1685, 0.6, -0.3333, -0.1685, 0.8, -0.3333, -0.1, 0.8, -0.5, -0.1, 0.8, -0.5, -0.1, 0.6, -0.5, -0.1685, 0.6, -0.3333, 0.0535, 0.4, -0.0535, 0.0535, 0.6, -0.0535, 0.1, 0.6, -0.1667, 0.1, 0.6, -0.1667, 0.1, 0.4, -0.1667, 0.0535, 0.4, -0.0535, 0.5, 0.2, -0.5, 0.3, 0.2, -0.5, 0.5, 0, -0.5, -0.5, 0, -0.5, 0.5, 0, -0.5, 0.3, 0.2, -0.5, 0.3, 0.2, -0.5, -0.5, 1, -0.5, -0.5, 0, -0.5, 0.3, 0.2, -0.5, 0.1, 0.4, -0.5, -0.5, 1, -0.5, 0.1, 0.4, -0.5, -0.1, 0.6, -0.5, -0.5, 1, -0.5, -0.1, 0.6, -0.5, -0.3, 0.8, -0.5, -0.5, 1, -0.5, -0.3, 0.8, -0.5, -0.3, 1, -0.5, -0.5, 1, -0.5, -0.1, 0.8, -0.5, -0.3, 0.8, -0.5, -0.1, 0.6, -0.5, 0.1, 0.6, -0.5, -0.1, 0.6, -0.5, 0.1, 0.4, -0.5, 0.3, 0.4, -0.5, 0.1, 0.4, -0.5, 0.3, 0.2, -0.5, -0.3685, 0.8, 0, -0.3, 0.8, 0.1667, -0.3, 1, 0.1667, -0.3, 1, 0.1667, -0.3685, 1, 0, -0.3685, 0.8, 0, 0.0315, 0.4, -0.3333, 0.0315, 0.6, -0.3333, 0.1, 0.6, -0.5, 0.1, 0.6, -0.5, 0.1, 0.4, -0.5, 0.0315, 0.4, -0.3333, -0.1685, 0.6, 0, -0.1685, 0.8, 0, -0.1, 0.8, -0.1667, -0.1, 0.8, -0.1667, -0.1, 0.6, -0.1667, -0.1685, 0.6, 0, -0.1, 0.6, -0.1667, -0.1, 0.8, -0.1667, -0.1685, 0.8, -0.3333, -0.1685, 0.8, -0.3333, -0.1685, 0.6, -0.3333, -0.1, 0.6, -0.1667, 0, 0.6, 0.1685, 0.1667, 0.6, 0.1, 0.1667, 0.8, 0.1, 0.1667, 0.8, 0.1, 0, 0.8, 0.1685, 0, 0.6, 0.1685, 0.2315, 0.2, -0.3333, 0.2315, 0.4, -0.3333, 0.3, 0.4, -0.5, 0.3, 0.4, -0.5, 0.3, 0.2, -0.5, 0.2315, 0.2, -0.3333, -0.3, 0.8, -0.1667, -0.3685, 0.8, 0, -0.3685, 1, 0, -0.3685, 1, 0, -0.3, 1, -0.1667, -0.3, 0.8, -0.1667, 0.1667, 0.8, 0.1, 0.1667, 0.6, 0.1, 0.3333, 0.6, 0.1685, 0.3333, 0.6, 0.1685, 0.3333, 0.8, 0.1685, 0.1667, 0.8, 0.1, 0.1, 0.4, -0.1667, 0.1, 0.6, -0.1667, 0.0315, 0.6, -0.3333, 0.0315, 0.6, -0.3333, 0.0315, 0.4, -0.3333, 0.1, 0.4, -0.1667, -0.3685, 0.8, -0.3333, -0.3, 0.8, -0.1667, -0.3, 1, -0.1667, -0.3, 1, -0.1667, -0.3685, 1, -0.3333, -0.3685, 0.8, -0.3333, -0.3, 0.8, 0.1667, -0.3685, 0.8, 0, -0.3, 0.8, -0.1667, -0.1685, 0.8, -0.3333, -0.3, 0.8, 0.1667, -0.3, 0.8, -0.1667, -0.1, 0.8, -0.5, -0.1685, 0.8, -0.3333, -0.3, 0.8, -0.1667, -0.3, 0.8, -0.5, -0.1, 0.8, -0.5, -0.3, 0.8, -0.1667, -0.3, 0.8, -0.1667, -0.3685, 0.8, -0.3333, -0.3, 0.8, -0.5, -0.3, 0.8, 0.1667, -0.1685, 0.8, -0.3333, -0.1685, 0.8, 0, -0.3, 0.8, 0.1667, -0.1685, 0.8, 0, -0.1667, 0.8, 0.3, -0.3, 0.8, 0.1667, -0.1667, 0.8, 0.3, -0.3333, 0.8, 0.3685, -0.3333, 0.8, 0.3685, -0.3685, 0.8, 0.3333, -0.3, 0.8, 0.1667, -0.1685, 0.8, 0, -0.1194, 0.8, 0.1194, -0.1667, 0.8, 0.3, -0.1194, 0.8, 0.1194, 0, 0.8, 0.3685, -0.1667, 0.8, 0.3, -0.1194, 0.8, 0.1194, 0, 0.8, 0.1685, 0, 0.8, 0.3685, 0, 0.8, 0.1685, 0.1667, 0.8, 0.3, 0, 0.8, 0.3685, 0, 0.8, 0.1685, 0.1667, 0.8, 0.1, 0.1667, 0.8, 0.3, 0.1667, 0.8, 0.1, 0.3333, 0.8, 0.3685, 0.1667, 0.8, 0.3, 0.1667, 0.8, 0.1, 0.3333, 0.8, 0.1685, 0.3333, 0.8, 0.3685, 0.3333, 0.8, 0.1685, 0.5, 0.8, 0.3, 0.3333, 0.8, 0.3685, 0.3333, 0.8, 0.1685, 0.5, 0.8, 0.1, 0.5, 0.8, 0.3, -0.1, 0.8, -0.1667, -0.1685, 0.8, 0, -0.1685, 0.8, -0.3333, 0, 0.8, 0.1685, -0.1194, 0.8, 0.1194, -0.084, 0.75, 0.084, -0.084, 0.75, 0.084, 0, 0.75, 0.1185, 0, 0.8, 0.1685, 0.1384, 0.55, -0.1384, 0.1667, 0.55, -0.15, 0.1667, 0.6, -0.1, 0.1667, 0.6, -0.1, 0.0535, 0.6, -0.0535, 0.1384, 0.55, -0.1384, -0.3333, 1, 0.3685, -0.3082, 0.95, 0.3081, -0.1667, 0.95, 0.25, -0.1667, 0.95, 0.25, -0.1667, 1, 0.3, -0.3333, 1, 0.3685, 0.3333, 0.4, -0.2315, 0.2612, 0.4, -0.2612, 0.2966, 0.35, -0.2966, 0.2966, 0.35, -0.2966, 0.3333, 0.35, -0.2815, 0.3333, 0.4, -0.2315, -0.3082, 0.95, 0.3081, -0.3333, 1, 0.3685, -0.3685, 1, 0.3333, -0.3185, 0.95, -0.3333, -0.3685, 1, -0.3333, -0.3, 1, -0.5, -0.3, 1, -0.5, -0.25, 0.95, -0.5, -0.3185, 0.95, -0.3333, 0.1667, 0.4, -0.1, 0.0535, 0.4, -0.0535, 0.1, 0.4, -0.1667, 0.2315, 0.4, -0.3333, 0.1667, 0.4, -0.1, 0.1, 0.4, -0.1667, 0.3333, 0.4, -0.0315, 0.1667, 0.4, -0.1, 0.2315, 0.4, -0.3333, 0.2315, 0.4, -0.3333, 0.2612, 0.4, -0.2612, 0.3333, 0.4, -0.0315, 0.2612, 0.4, -0.2612, 0.3333, 0.4, -0.2315, 0.3333, 0.4, -0.0315, 0.3333, 0.4, -0.2315, 0.5, 0.4, -0.1, 0.3333, 0.4, -0.0315, 0.3333, 0.4, -0.2315, 0.5, 0.4, -0.3, 0.5, 0.4, -0.1, 0.3, 0.4, -0.5, 0.2315, 0.4, -0.3333, 0.1, 0.4, -0.1667, 0.1, 0.4, -0.5, 0.3, 0.4, -0.5, 0.1, 0.4, -0.1667, 0.1, 0.4, -0.1667, 0.0315, 0.4, -0.3333, 0.1, 0.4, -0.5, 0.1667, 1, 0.3, 0.1667, 0.95, 0.25, 0.25, 0.975, 0.3092, 0.25, 0.975, 0.3092, 0.3333, 1, 0.3685, 0.1667, 1, 0.3, 0.1667, 0.95, 0.25, 0.2083, 0.95, 0.2671, 0.25, 0.975, 0.3092, 0.25, 0.975, 0.3092, 0.2917, 0.95, 0.3014, 0.3333, 1, 0.3685, 0.2917, 0.95, 0.3014, 0.3333, 0.95, 0.3185, 0.3333, 1, 0.3685, -0.3, 1, -0.1667, -0.3685, 1, -0.3333, -0.3185, 0.95, -0.3333, -0.3185, 0.95, -0.3333, -0.25, 0.95, -0.1667, -0.3, 1, -0.1667, 0.2612, 0.2, -0.2612, 0.2315, 0.2, -0.3333, 0.3, 0.2, -0.5, 0.3, 0.2, -0.5, 0.3333, 0.2, -0.2315, 0.2612, 0.2, -0.2612, 0.3, 0.2, -0.5, 0.5, 0.2, -0.5, 0.3333, 0.2, -0.2315, 0.5, 0.2, -0.5, 0.5, 0.2, -0.3, 0.3333, 0.2, -0.2315, -0.1685, 0.8, -0.3333, -0.1185, 0.75, -0.3333, -0.1093, 0.775, -0.25, -0.1093, 0.775, -0.25, -0.1, 0.8, -0.1667, -0.1685, 0.8, -0.3333, -0.1093, 0.775, -0.25, -0.0671, 0.75, -0.2083, -0.1, 0.8, -0.1667, -0.0671, 0.75, -0.2083, -0.05, 0.75, -0.1667, -0.1, 0.8, -0.1667, -0.1014, 0.75, -0.2917, -0.1093, 0.775, -0.25, -0.1185, 0.75, -0.3333, 0.3333, 1, 0.3685, 0.3333, 0.95, 0.3185, 0.5, 0.95, 0.25, 0.5, 0.95, 0.25, 0.5, 1, 0.3, 0.3333, 1, 0.3685, -0.3, 1, 0.1667, -0.3685, 1, 0, -0.3185, 0.95, 0, -0.3185, 0.95, 0, -0.25, 0.95, 0.1667, -0.3, 1, 0.1667, 0.3333, 0.4, -0.2315, 0.3333, 0.35, -0.2815, 0.375, 0.35, -0.2986, 0.375, 0.35, -0.2986, 0.4167, 0.375, -0.2908, 0.3333, 0.4, -0.2315, 0.5, 0.4, -0.3, 0.3333, 0.4, -0.2315, 0.4167, 0.375, -0.2908, 0.5, 0.35, -0.35, 0.5, 0.4, -0.3, 0.4167, 0.375, -0.2908, 0.4167, 0.375, -0.2908, 0.4583, 0.35, -0.3329, 0.5, 0.35, -0.35, 0.3333, 0.6, -0.0315, 0.3333, 0.55, -0.0815, 0.5, 0.55, -0.15, 0.5, 0.55, -0.15, 0.5, 0.6, -0.1, 0.3333, 0.6, -0.0315, 0, 1, 0.3685, -0.1667, 1, 0.3, -0.1667, 0.95, 0.25, -0.1667, 0.95, 0.25, 0, 0.95, 0.3185, 0, 1, 0.3685, 0, 0.75, 0.1185, 0.1667, 0.75, 0.05, 0.1667, 0.8, 0.1, 0.1667, 0.8, 0.1, 0, 0.8, 0.1685, 0, 0.75, 0.1185, 0.2612, 0.4, -0.2612, 0.2315, 0.4, -0.3333, 0.2815, 0.35, -0.3333, 0.2815, 0.35, -0.3333, 0.2966, 0.35, -0.2966, 0.2612, 0.4, -0.2612, -0.5, 1, -0.5, -0.3, 1, -0.5, -0.3685, 1, -0.3333, -0.3685, 1, -0.3333, -0.5, 1, 0.5, -0.5, 1, -0.5, -0.5, 1, 0.5, -0.3685, 1, -0.3333, -0.3685, 1, 0, -0.3685, 1, 0, -0.3685, 1, 0.3333, -0.5, 1, 0.5, -0.3685, 1, 0.3333, -0.3333, 1, 0.3685, -0.5, 1, 0.5, -0.3333, 1, 0.3685, 0.5, 1, 0.5, -0.5, 1, 0.5, -0.3333, 1, 0.3685, 0, 1, 0.3685, 0.5, 1, 0.5, -0.3333, 1, 0.3685, -0.1667, 1, 0.3, 0, 1, 0.3685, 0, 1, 0.3685, 0.1667, 1, 0.3, 0.5, 1, 0.5, 0.1667, 1, 0.3, 0.3333, 1, 0.3685, 0.5, 1, 0.5, 0.3333, 1, 0.3685, 0.5, 1, 0.3, 0.5, 1, 0.5, -0.3, 1, 0.1667, -0.3685, 1, 0.3333, -0.3685, 1, 0, -0.3, 1, -0.1667, -0.3685, 1, 0, -0.3685, 1, -0.3333, 0.3333, 0.8, 0.1685, 0.1667, 0.8, 0.1, 0.1667, 0.75, 0.05, 0.1667, 0.75, 0.05, 0.3333, 0.75, 0.1185, 0.3333, 0.8, 0.1685, 0.3333, 0.6, -0.0315, 0.1667, 0.6, -0.1, 0.1667, 0.55, -0.15, 0.1667, 0.55, -0.15, 0.3333, 0.55, -0.0815, 0.3333, 0.6, -0.0315, 0.2815, 0.35, -0.3333, 0.2315, 0.4, -0.3333, 0.3, 0.4, -0.5, 0.3, 0.4, -0.5, 0.35, 0.35, -0.5, 0.2815, 0.35, -0.3333, -0.3685, 1, 0.3333, -0.3, 1, 0.1667, -0.3093, 0.975, 0.25, -0.3093, 0.975, 0.25, -0.3082, 0.95, 0.3081, -0.3685, 1, 0.3333, -0.3093, 0.975, 0.25, -0.3014, 0.95, 0.2917, -0.3082, 0.95, 0.3081, -0.3, 1, 0.1667, -0.2671, 0.95, 0.2083, -0.3093, 0.975, 0.25, -0.3, 1, 0.1667, -0.25, 0.95, 0.1667, -0.2671, 0.95, 0.2083, -0.1185, 0.75, -0.3333, -0.1685, 0.8, -0.3333, -0.1, 0.8, -0.5, -0.1, 0.8, -0.5, -0.05, 0.75, -0.5, -0.1185, 0.75, -0.3333, -0.1194, 0.8, 0.1194, -0.1685, 0.8, 0, -0.1185, 0.75, 0, -0.1185, 0.75, 0, -0.084, 0.75, 0.084, -0.1194, 0.8, 0.1194, 0.1, 0.6, -0.1667, 0.0315, 0.6, -0.3333, 0.0815, 0.55, -0.3333, 0.0815, 0.55, -0.3333, 0.15, 0.55, -0.1667, 0.1, 0.6, -0.1667, 0.3333, 0.8, 0.1685, 0.3333, 0.75, 0.1185, 0.5, 0.75, 0.05, 0.5, 0.75, 0.05, 0.5, 0.8, 0.1, 0.3333, 0.8, 0.1685, -0.1194, 0.6, 0.1194, -0.1685, 0.6, 0, -0.1, 0.6, -0.1667, -0.1, 0.6, -0.1667, 0, 0.6, 0.1685, -0.1194, 0.6, 0.1194, 0.0315, 0.6, -0.3333, 0, 0.6, 0.1685, -0.1, 0.6, -0.1667, 0.1, 0.6, -0.5, 0.0315, 0.6, -0.3333, -0.1, 0.6, -0.1667, -0.1, 0.6, -0.5, 0.1, 0.6, -0.5, -0.1, 0.6, -0.1667, -0.1, 0.6, -0.1667, -0.1685, 0.6, -0.3333, -0.1, 0.6, -0.5, 0, 0.6, 0.1685, 0.0315, 0.6, -0.3333, 0.0535, 0.6, -0.0535, 0.0535, 0.6, -0.0535, 0.1667, 0.6, 0.1, 0, 0.6, 0.1685, 0.0535, 0.6, -0.0535, 0.1667, 0.6, -0.1, 0.1667, 0.6, 0.1, 0.1667, 0.6, -0.1, 0.3333, 0.6, 0.1685, 0.1667, 0.6, 0.1, 0.1667, 0.6, -0.1, 0.3333, 0.6, -0.0315, 0.3333, 0.6, 0.1685, 0.3333, 0.6, -0.0315, 0.5, 0.6, 0.1, 0.3333, 0.6, 0.1685, 0.3333, 0.6, -0.0315, 0.5, 0.6, -0.1, 0.5, 0.6, 0.1, 0.1, 0.6, -0.1667, 0.0535, 0.6, -0.0535, 0.0315, 0.6, -0.3333, 0, 0.95, 0.3185, 0.1667, 0.95, 0.25, 0.1667, 1, 0.3, 0.1667, 1, 0.3, 0, 1, 0.3685, 0, 0.95, 0.3185, -0.1185, 0.75, 0, -0.1685, 0.8, 0, -0.1, 0.8, -0.1667, -0.1, 0.8, -0.1667, -0.05, 0.75, -0.1667, -0.1185, 0.75, 0, 0.1384, 0.55, -0.1384, 0.0535, 0.6, -0.0535, 0.1, 0.6, -0.1667, 0.1, 0.6, -0.1667, 0.15, 0.55, -0.1667, 0.1384, 0.55, -0.1384, 0.0815, 0.55, -0.3333, 0.0315, 0.6, -0.3333, 0.1, 0.6, -0.5, 0.1, 0.6, -0.5, 0.15, 0.55, -0.5, 0.0815, 0.55, -0.3333, -0.3185, 0.95, 0, -0.3685, 1, 0, -0.3, 1, -0.1667, -0.3, 1, -0.1667, -0.25, 0.95, -0.1667, -0.3185, 0.95, 0)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_kqvwt"]
resource_name = "dirt"
albedo_color = Color(0.948242, 0.744326, 0.619001, 1)
metallic = 1.0

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_jv03e"]
resource_name = "grass"
albedo_color = Color(0.45234, 0.929502, 0.865877, 1)
metallic = 1.0

[sub_resource type="ArrayMesh" id="ArrayMesh_e4kj3"]
_surfaces = [{
"aabb": AABB(-0.5, -3.60956e-15, -0.5, 1, 1, 1),
"format": ***********,
"index_count": 267,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYACAAEAAYABgAJAAgADAAKAAsACwANAAwAEAAOAA8ADwARABAAFAASABMAEwAVABQAGAAWABcAFwAZABgAHAAaABsAGwAdABwAIAAeAB8AHwAhACAAJAAiACMAIwAlACQAKAAmACcAJwApACgAKwAqAB4AHgAgACsALgAsAC0ALQAvAC4AMQAwAAIAAgADADEADwAyADMAMwARAA8AEwAWABgAGAAfABMAHwA0ABMANAAKABMACgA1ABMANQAVABMANgA1AAoACwAKADQANwA0AB8AIQAfABgAHAAZABcAFwAaABwAFwAOABoADgAiABoAIgAkABoADgAmACIAJgAoACIADgAwACYAMAAxACYADgAQADAAOgA4ADkAOQA7ADoAPAA1ADYANgA9ADwAPwA+AAwADAANAD8AQgBAAEEAQQBDAEIARQBEAAgACAAJAEUALQAsADwAPAA9AC0AJwA4ADoAOgApACcARwBGABIAEgAUAEcAIwBEAEUARQAlACMAQQAqACsAKwBDAEEADgAXABYAFgAPAA4AFgAyAA8AFgBIADIAFgBGAEgAFgASAEYAFgATABIASABGAEcARwBJAEgAAQAAAC4ALgAvAAEAGwBAAEIAQgAdABsAMwAyAEgASABJADMAOQA+AD8APwA7ADkABQA0ADcANwAHAAUA"),
"name": "dirt",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 74,
"vertex_data": PackedByteArray("VFUzMzIzAABUVWZmMjMAAKoqMzO8RAAAqipmZrxEAACqqpmZmZkAAFTVmZkiqwAAqqrMzJmZAABU1czMIqsAAP9/mZkiqwAA/3/MzCKrAAD//2ZmZmYAAP//mZlmZgAAVNVmZu93AABU1ZmZ73cAAAAAAAAAAAAAqioAAIkRAAAAADMzAAAAAKoqMzOJEQAAVNUAAIkRAAD//wAAAAAAAFTVMzOJEQAA//8zMwAAAAD//wAA//8AAAAAAAD//wAA////////AAAAAP////8AAAAAzMzMzAAAqirMzFXeAAAAAP//zMwAAKoq//9V3gAAVNXMzFXeAAD//8zMzMwAAFTV//9V3gAA/////8zMAAAAAJmZmZkAAKoqmZkiqwAAAADMzJmZAACqKszMIqsAAAAAZmZmZgAAqipmZu93AAAAAJmZZmYAAKoqmZnvdwAAqqrMzMzMAACqqv//zMwAAKqqMzMyMwAAqqpmZjIzAAD/fzMzvEQAAP9/Zma8RAAAAAAzMzIzAAAAAGZmMjMAAFRVAAAAAAAAVFUzMwAAAAD//5mZmZkAAP//MzMyMwAA//9mZjIzAAD//8zMmZkAAFRVZmZmZgAA/39mZu93AABUVZmZZmYAAP9/mZnvdwAAVNUzM7xEAABU1WZmvEQAAKqqZmZmZgAAqqqZmWZmAABUVczMzMwAAP9/zMxV3gAAVFX//8zMAAD/f///Vd4AAFRVmZmZmQAAVFXMzJmZAACqqgAAAAAAAKqqMzMAAAAA/38AAIkRAAD/fzMziREAAA==")
}, {
"aabb": AABB(-0.5, 0.15, -0.55, 1, 0.85, 1.05),
"format": ***********,
"index_count": 390,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAwAEAAIAAwAFAAQABQAGAAQABQAHAAYABwAIAAYABwAJAAgACQAKAAgACQALAAoACwAMAAoACwANAAwAEAAOAA8ADwARABAADwASABEADwATABIAEgAUABEAFAAVABEAFQAWABEAGQAXABgAGAAaABkAGgAbABkAGgAcABsAHAAdABsAHAAeAB0AHgAfAB0AHgAgAB8AIAAhAB8AIAAiACEAIgAjACEAIgAkACMABwAFACUAJQAmAAcAKQAnACgAKAAqACkALAArABQAFAASACwAKQAqAC0ALQAuACkAMQAvADAAMAAyADEAIgAzADQANAA1ACIAJAAiADUANgAkADUANQA3ADYAOgA4ADkAOQA7ADoAOwA8ADoAOwA9ADwAPQA+ADwAPQA/AD4APwBAAD4APwBBAEAAQQBCAEAAQQBDAEIAQwBEAEIAQwBFAEQAFQBGAEcARwAWABUADwAOAEgASABJAA8AGgAYAEoASgBLABoAIgAgAEwATAAzACIATQBMACAAIAAeAE0APwA9AE4ATgBPAD8ATwBQAEEAQQA/AE8AQwBBAFAAUABRAEMAOwBSAFMAUwBUADsAPQA7AFQATgA9AFQAVABVAE4ACwBWAFcAVwANAAsAWAAwAC8ALwBZAFgAMgAuAC0ALQBaADIALQBbAFoAWgBcADIAXAAxADIAQwBRAF0AXQBFAEMAOwA5AF4AXgBSADsAFQAUACsAKwBfABUAKwBgAF8AXwBhABUAYQBGABUADwBJAGIAYgATAA8AGgBLAGMAYwAcABoAWABZAGQAZABlAFgAHgAcAGMAYwBNAB4AZwBmACcAJwApAGcAKQBoAGcAKQAuAGgALgBpAGgALgAyAGkAMgBqAGkAMgAwAGoAMABrAGoAMABYAGsAWABsAGsAWABlAGwAEgATAGIAYgAsABIAJgBtAAkACQAHACYAAwABAG4AbgBvAAMACwAJAG0AbQBWAAsAAwBvAHAAcABxAAMABQADAHEAJQAFAHEAcQByACUA"),
"name": "grass",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 115,
"vertex_data": PackedByteArray("AADCwzzPAAAAAMLDeZ4AAKoqwsPv3wAAqirCwyyvAABUVcLDPM8AAFRVwsN5ngAA/3/Cw+/fAAD/f8LDLK8AAKqqwsM8zwAAqqrCw3meAABU1cLD798AAFTVwsMsrwAA///CwzzPAAD//8LDeZ4AAAAA//88zwAAqir//+/fAAAAAP////8AAP///////wAA/3///+/fAABUVf//PM8AAKqq//88zwAAVNX//+/fAAD/////PM8AAAAASku2bQAAAABKS/M8AACqKkpLaX4AAKoqSkumTQAAVFVKS7ZtAABUVUpL8zwAAP9/SktpfgAA/39KS6ZNAACqqkpLtm0AAKqqSkvzPAAAVNVKS2l+AABU1UpLpk0AAP//Sku2bQAA//9KS/M8AABUVbS0SJIAAP9/tLT7ogAAAACHh7ZtAAAAAHh4hWEAAKoqh4dpfgAAqip4eDlyAACqqu/wC8MAAP9/7/C+0wAAVFV4eIVhAABUVYeHtm0AAKqqeHiFYQAAqqqHh7ZtAAD/f3h4OXIAAP9/h4dpfgAAVNU7PHZBAAD/3zs8ST0AAKnqw0M0PwAA//87PMIwAABU9Ts87zQAAAAADg/zPAAAAAAODzAMAACqKg4Ppk0AAKoqDg/kHAAAVFUOD/M8AABUVQ4PMAwAAP9/Dg+mTQAA/38OD+QcAACqqg4P8zwAAKqqDg8wDAAAVNUOD6ZNAABU1Q4P5BwAAP//Dg/zPAAA//8ODzAMAABU1e/wvtMAAP//7/ALwwAAAADv8AvDAACqKu/wvtMAAAAAOzzCMAAAqio7PHZBAACqqjs8wjAAAP9/Ozx2QQAAVFUAAAAAAAD/fwAAsxAAAKqqAAAAAAAAVNUAALMQAACqKgAAsxAAAFU1AACGDAAA/z+HB3IOAACqSgAALAQAAFTVtLT7ogAA//+0tEiSAABU1YeHaX4AAFTVeHg5cgAAqmr/f/dvAAD/X3h4smUAAFR1eHgMbgAA//8AAAAAAAAAAAAAAAAAAP+/d/h90QAAVLXv8DjHAACpyu/wkc8AAFRV7/ALwwAAVFU7PMIwAAD//3h4hWEAAP//h4e2bQAAAACHh3meAACqKoeHLK8AAFRVh4d5ngAA/3+HhyyvAACqqoeHeZ4AAFTVh4csrwAA//+Hh3meAACqqrS0SJIAAAAAtLRIkgAAqiq0tPuiAABVNbS0z54AAP8/O7y6oAAAqkq0tHWWAAA=")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_52ixr"]
resource_name = "cliff_steps_rock_Mesh cliff_steps_rock"
_surfaces = [{
"aabb": AABB(-0.5, -3.60956e-15, -0.5, 1, 1, 1),
"attribute_data": PackedByteArray("3YcRad2HzE42qxFpNqvMTk1aiDT1Nog0TVpEGvU2RBo3aYg0N2lEGpCMiDSQjEQa0x3MTtMdiDQsQcxOLEGINP//VYOm3FWD//8RaabcEWlYI1WDAABVg1gjEWkAABFpE+JVg+sdVYMT4gAA6x0AAFfERBr/oEQaV8QAAP+gAAD/XkQapztEGv9eAACnOwAAQdOINOiviDRB00Qa6K9EGivizE7SvsxOK+KINNK+iDRjS0QaCyhEGmNLAAALKAAAY0sRaWNLzE68bhFpvG7MThXxEWm8zRFpFfHMTrzNzE7zeFWD83gRaUycVYNMnBFp6x1Vg+sdAAAT4lWDJkVEGmFsiDSdk8xO2LoRaRPiEWnYusxOnZOINGFsRBomRQAAE+IAABPiVYPYugAA2LpEGusdVYOdk4g0nZNEGmFszE5hbIg0JkURaSZFzE7rHRFpsaXMTliCzE6xpYg0WIKINOkOEWnpDsxOQjIRaUIyzE43acxO3kXMTjdpiDTeRYg03YdEGoVkRBrdhwAAhWQAAMeWiDRuc4g0x5ZEGm5zRBoheBFpyFQRaSF4zE7IVMxOx5bMTseWiDQgusxOILqINAuHVYOyY1WDC4cRabJjEWmxpYg0saVEGgnJiDQJyUQaIXhEGiF4AAB5m0QaeZsAABPiAMXrHQDFE+KqQWLBqUqwoKpB/3+pSk5fqkGcPqlK6x2qQXk8VYN5PBFp0l9Vg9JfEWmbtBFpQpERaZu0zE5CkcxOm7REGpu0AADz10Qa89cAAIXDVYMsoFWDhcMRaSygEWlNWsxOTVqINKZ9zE6mfYg0vSyINL0sRBoWUIg0FlBEGg=="),
"format": ***********,
"index_count": 267,
"index_data": PackedByteArray("AgAAAAEAAQADAAIABgAEAAUABQAHAAYACgAIAAkACQALAAoADgAMAA0ADQAPAA4AEgAQABEAEQATABIAFgAUABUAFQAXABYAGgAYABkAGQAbABoAHgAcAB0AHQAfAB4AIgAgACEAIQAjACIAJgAkACUAJQAnACYAKgAoACkAKQArACoALgAsAC0ALQAvAC4AMgAwADEAMQAzADIANgA0ADUANQA3ADYAOgA4ADkAOQA7ADoAPgA8AD0APQA/AD4APwBAAD4AQABBAD4AQQBCAD4AQgBDAD4ARABCAEEARQBBAEAARgBAAD8ARwA/AD0ASgBIAEkASQBLAEoASQBMAEsATABNAEsATQBOAEsATABPAE0ATwBQAE0ATABRAE8AUQBSAE8ATABTAFEAVgBUAFUAVQBXAFYAWgBYAFkAWQBbAFoAXgBcAF0AXQBfAF4AYgBgAGEAYQBjAGIAZgBkAGUAZQBnAGYAagBoAGkAaQBrAGoAbgBsAG0AbQBvAG4AcgBwAHEAcQBzAHIAdgB0AHUAdQB3AHYAegB4AHkAeQB7AHoAfgB8AH0AfQB/AH4AfQCAAH8AfQCBAIAAfQCCAIEAfQCDAIIAfQCEAIMAhwCFAIYAhgCIAIcAiwCJAIoAigCMAIsAjwCNAI4AjgCQAI8AkwCRAJIAkgCUAJMAlwCVAJYAlgCYAJcAmwCZAJoAmgCcAJsA"),
"material": SubResource("StandardMaterial3D_kqvwt"),
"name": "dirt",
"primitive": 3,
"uv_scale": Vector4(51.3808, 76.7402, 0, 0),
"vertex_count": 157,
"vertex_data": PackedByteArray("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")
}, {
"aabb": AABB(-0.5, 0.15, -0.55, 1, 0.85, 1.05),
"attribute_data": PackedByteArray("siCVqLIgo492QCGxdkAumDtglag7YKOP/38hsf9/LpjDn5Wow5+jj4i/IbGIvy6YTN+VqEzfo4+yIJWodkAhsbIgiMFM34jB/38hsTtglajDn5WoiL8hsUzflaiyILF2siC/XXZAPH92QEpmO2Cxdjtgv13/fzx//39KZsOfsXbDn79diL88f4i/SmZM37F2TN+/XSKWtizBmTU1ynO2LGl3NTVj3/ZDA+N0TAu99kOqwHRMunQ6E1p4uwoTlzoTspq7Ctu062GDkutherhsWSKWbFm/VzBXXluxThd6MFe3fbFO1jBvbkAob251NPFleSEwah0S8WV9Dm9uExdvbrIgv12yIM1EdkBKZnZAWE07YL9dO2DNRP9/Smb/f1hNw5+/XcOfzUSIv0pmiL9YTUzfv11M381ET1x+CPc5fgjvXwAAlj0AAGjCAAAHxn4ID6AAAK+jfgjh7fFlgfFvbonL8WUoz29uWnhne/l75oMBVmd7oVnmg0FJK3ngTKxwmWsreTlvrHCbwaeSO8Umm0Ofp5Lioiabwzomm2M+p5IcXSabu2CnktiGYp13iuGlf2RinR9o4aXfl+GlSY/hpX+bYp2DiKKhJnlinYd14aUdfuGl0U15KnkreSpxUfshGC/7IdxpbFl7bethhEdsWSNL62GgpLFOP6gwV0eCsU5DlfBSqZ8wV32OMFfnhTBXWCJqkAAAapD3JeyHnwPsh1/87If//2qQB9rsh6bdapDgTHYVgFD1HYgqdhWEPbYZ6kf1Hb429R0nLvUd19H1HX6v9R121XYVHrN2FV2m5oMFhOaD/alne6SHZ3tUP3RM+xx0TPNC9kObIPZDHrOscL22K3nFkKxwZZQrebIgo4+yILF2dkAumHZAPH87YKOPO2Cxdv9/Lpj/fzx/w5+jj8OfsXaIvy6YiL88f0zfo49M37F2pIe7CkSLOhNMZbsK62g6Ez1mNTXcabYslYg1NTSMtizm0PshhdR5Ko2u+yEtsnkqXltxN/5e8D8GOXE3pTzwP1nD8D/DuvA/+MZxN/yzsTugpHE3AKHwP5ap8D8="),
"format": ***********,
"index_count": 390,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAwAEAAIAAwAFAAQABQAGAAQABQAHAAYABwAIAAYABwAJAAgACQAKAAgACQALAAoACwAMAAoACwANAAwAEAAOAA8ADwARABAADwASABEADwATABIAEgAUABEAFAAVABEAFQAWABEAGQAXABgAGAAaABkAGgAbABkAGgAcABsAHAAdABsAHAAeAB0AHgAfAB0AHgAgAB8AIAAhAB8AIAAiACEAIgAjACEAIgAkACMAJwAlACYAJgAoACcAKwApACoAKgAsACsALwAtAC4ALgAwAC8AMwAxADIAMgA0ADMANwA1ADYANgA4ADcAOwA5ADoAOgA8ADsAPQA7ADwAPgA9ADwAPAA/AD4AQgBAAEEAQQBDAEIAQwBEAEIAQwBFAEQARQBGAEQARQBHAEYARwBIAEYARwBJAEgASQBKAEgASQBLAEoASwBMAEoASwBNAEwAUABOAE8ATwBRAFAAVABSAFMAUwBVAFQAWABWAFcAVwBZAFgAXABaAFsAWwBdAFwAYABeAF8AXwBhAGAAZABiAGMAYwBlAGQAaABmAGcAZwBpAGgAbABqAGsAawBtAGwAcABuAG8AbwBxAHAAcgBwAHEAcwByAHEAcQB0AHMAdwB1AHYAdgB4AHcAewB5AHoAegB8AHsAfwB9AH4AfgCAAH8AfgCBAIAAgACCAH8AggCDAH8AhgCEAIUAhQCHAIYAigCIAIkAiQCLAIoAjgCMAI0AjQCPAI4AjQCQAI8AjwCRAI4AkQCSAI4AlQCTAJQAlACWAJUAmQCXAJgAmACaAJkAnQCbAJwAnACeAJ0AoQCfAKAAoACiAKEApQCjAKQApACmAKUApgCnAKUApgCoAKcAqACpAKcAqACqAKkAqgCrAKkAqgCsAKsArACtAKsArACuAK0ArgCvAK0ArgCwAK8AswCxALIAsgC0ALMAtwC1ALYAtgC4ALcAuwC5ALoAugC8ALsAvwC9AL4AvgDAAL8AwwDBAMIAwgDEAMMAxQDDAMQAxgDFAMQAxADHAMYA"),
"material": SubResource("StandardMaterial3D_jv03e"),
"name": "grass",
"primitive": 3,
"uv_scale": Vector4(52.8774, 80.8031, 0, 0),
"vertex_count": 200,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_e4kj3")

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_af17c"]
data = PackedVector3Array(-0.3333, 0.2, -0.2315, -0.1667, 0.2, -0.3, -0.1667, 0.4, -0.3, -0.1667, 0.4, -0.3, -0.3333, 0.4, -0.2315, -0.3333, 0.2, -0.2315, 0.1667, 0.8, 0.1, 0.1667, 0.6, 0.1, 0.3333, 0.6, 0.1685, 0.3333, 0.6, 0.1685, 0.3333, 0.8, 0.1685, 0.1667, 0.8, 0.1, 0, 0.6, 0.1685, 0.1667, 0.6, 0.1, 0.1667, 0.8, 0.1, 0.1667, 0.8, 0.1, 0, 0.8, 0.1685, 0, 0.6, 0.1685, 0.3333, 0.4, -0.0315, 0.5, 0.4, -0.1, 0.5, 0.6, -0.1, 0.5, 0.6, -0.1, 0.3333, 0.6, -0.0315, 0.3333, 0.4, -0.0315, -0.5, 0.2, -0.5, -0.5, 0, -0.5, -0.3333, 0, -0.4315, -0.3333, 0, -0.4315, -0.3333, 0.2, -0.4315, -0.5, 0.2, -0.5, 0.3333, 0.2, -0.4315, 0.3333, 0, -0.4315, 0.5, 0, -0.5, 0.5, 0, -0.5, 0.5, 0.2, -0.5, 0.3333, 0.2, -0.4315, 0.5, 1, 0.5, 0.5, 0, 0.5, -0.5, 0, 0.5, -0.5, 0, 0.5, -0.5, 1, 0.5, 0.5, 1, 0.5, -0.5, 1, 0.3, -0.5, 0.8, 0.3, -0.3333, 0.8, 0.3685, -0.3333, 0.8, 0.3685, -0.3333, 1, 0.3685, -0.5, 1, 0.3, 0.3333, 1, 0.3685, 0.3333, 0.8, 0.3685, 0.5, 0.8, 0.3, 0.5, 0.8, 0.3, 0.5, 1, 0.3, 0.3333, 1, 0.3685, -0.5, 0.8, 0.1, -0.5, 0.6, 0.1, -0.3333, 0.6, 0.1685, -0.3333, 0.6, 0.1685, -0.3333, 0.8, 0.1685, -0.5, 0.8, 0.1, -0.5, 0.6, -0.1, -0.5, 0.4, -0.1, -0.3333, 0.4, -0.0315, -0.3333, 0.4, -0.0315, -0.3333, 0.6, -0.0315, -0.5, 0.6, -0.1, 0.1667, 1, 0.3, 0.1667, 0.8, 0.3, 0.3333, 0.8, 0.3685, 0.3333, 0.8, 0.3685, 0.3333, 1, 0.3685, 0.1667, 1, 0.3, 0, 0.2, -0.2315, 0.1667, 0.2, -0.3, 0.1667, 0.4, -0.3, 0.1667, 0.4, -0.3, 0, 0.4, -0.2315, 0, 0.2, -0.2315, -0.5, 0.4, -0.3, -0.5, 0.2, -0.3, -0.3333, 0.2, -0.2315, -0.3333, 0.2, -0.2315, -0.3333, 0.4, -0.2315, -0.5, 0.4, -0.3, -0.3333, 0, -0.4315, -0.1667, 0, -0.5, -0.1667, 0.2, -0.5, -0.1667, 0.2, -0.5, -0.3333, 0.2, -0.4315, -0.3333, 0, -0.4315, 0.5, 0, -0.5, 0.5, 0, 0.5, 0.5, 1, 0.5, 0.5, 1, 0.5, 0.5, 0.8, 0.3, 0.5, 0, -0.5, 0.5, 0.8, 0.3, 0.5, 0.6, 0.1, 0.5, 0, -0.5, 0.5, 0.6, 0.1, 0.5, 0.4, -0.1, 0.5, 0, -0.5, 0.5, 0.4, -0.1, 0.5, 0.2, -0.3, 0.5, 0, -0.5, 0.5, 0.2, -0.3, 0.5, 0.2, -0.5, 0.5, 0, -0.5, 0.5, 0.4, -0.3, 0.5, 0.2, -0.3, 0.5, 0.4, -0.1, 0.5, 0.6, -0.1, 0.5, 0.4, -0.1, 0.5, 0.6, 0.1, 0.5, 0.8, 0.1, 0.5, 0.6, 0.1, 0.5, 0.8, 0.3, 0.5, 1, 0.3, 0.5, 0.8, 0.3, 0.5, 1, 0.5, -0.5, 1, 0.3, -0.5, 1, 0.5, -0.5, 0, 0.5, -0.5, 0, 0.5, -0.5, 0.8, 0.3, -0.5, 1, 0.3, -0.5, 0, 0.5, -0.5, 0, -0.5, -0.5, 0.8, 0.3, -0.5, 0, -0.5, -0.5, 0.6, 0.1, -0.5, 0.8, 0.3, -0.5, 0.6, 0.1, -0.5, 0.8, 0.1, -0.5, 0.8, 0.3, -0.5, 0, -0.5, -0.5, 0.4, -0.1, -0.5, 0.6, 0.1, -0.5, 0.4, -0.1, -0.5, 0.6, -0.1, -0.5, 0.6, 0.1, -0.5, 0, -0.5, -0.5, 0.2, -0.3, -0.5, 0.4, -0.1, -0.5, 0.2, -0.3, -0.5, 0.4, -0.3, -0.5, 0.4, -0.1, -0.5, 0, -0.5, -0.5, 0.2, -0.5, -0.5, 0.2, -0.3, -0.1667, 0.6, -0.1, -0.1667, 0.4, -0.1, 0, 0.4, -0.0315, 0, 0.4, -0.0315, 0, 0.6, -0.0315, -0.1667, 0.6, -0.1, 0.3333, 0.2, -0.2315, 0.5, 0.2, -0.3, 0.5, 0.4, -0.3, 0.5, 0.4, -0.3, 0.3333, 0.4, -0.2315, 0.3333, 0.2, -0.2315, 0.1667, 0.6, -0.1, 0.1667, 0.4, -0.1, 0.3333, 0.4, -0.0315, 0.3333, 0.4, -0.0315, 0.3333, 0.6, -0.0315, 0.1667, 0.6, -0.1, -0.1667, 1, 0.3, -0.1667, 0.8, 0.3, 0, 0.8, 0.3685, 0, 0.8, 0.3685, 0, 1, 0.3685, -0.1667, 1, 0.3, -0.1667, 0.8, 0.1, -0.1667, 0.6, 0.1, 0, 0.6, 0.1685, 0, 0.6, 0.1685, 0, 0.8, 0.1685, -0.1667, 0.8, 0.1, 0.1667, 0.4, -0.3, 0.1667, 0.2, -0.3, 0.3333, 0.2, -0.2315, 0.3333, 0.2, -0.2315, 0.3333, 0.4, -0.2315, 0.1667, 0.4, -0.3, -0.3333, 0.4, -0.0315, -0.1667, 0.4, -0.1, -0.1667, 0.6, -0.1, -0.1667, 0.6, -0.1, -0.3333, 0.6, -0.0315, -0.3333, 0.4, -0.0315, 0.1667, 0.2, -0.5, 0.1667, 0, -0.5, 0.3333, 0, -0.4315, 0.3333, 0, -0.4315, 0.3333, 0.2, -0.4315, 0.1667, 0.2, -0.5, -0.3333, 0.6, 0.1685, -0.1667, 0.6, 0.1, -0.1667, 0.8, 0.1, -0.1667, 0.8, 0.1, -0.3333, 0.8, 0.1685, -0.3333, 0.6, 0.1685, 0, 0.8, 0.3685, 0.1667, 0.8, 0.3, 0.1667, 1, 0.3, 0.1667, 1, 0.3, 0, 1, 0.3685, 0, 0.8, 0.3685, -0.5, 0, -0.5, -0.5, 0, 0.5, 0.5, 0, 0.5, 0.5, 0, 0.5, -0.3333, 0, -0.4315, -0.5, 0, -0.5, 0.5, 0, 0.5, -0.1667, 0, -0.5, -0.3333, 0, -0.4315, 0.5, 0, 0.5, 0, 0, -0.4315, -0.1667, 0, -0.5, 0.5, 0, 0.5, 0.1667, 0, -0.5, 0, 0, -0.4315, 0.5, 0, 0.5, 0.3333, 0, -0.4315, 0.1667, 0, -0.5, 0.5, 0, 0.5, 0.5, 0, -0.5, 0.3333, 0, -0.4315, 0, 0, -0.4315, 0.1667, 0, -0.5, 0.1667, 0.2, -0.5, 0.1667, 0.2, -0.5, 0, 0.2, -0.4315, 0, 0, -0.4315, -0.1667, 0.4, -0.3, -0.1667, 0.2, -0.3, 0, 0.2, -0.2315, 0, 0.2, -0.2315, 0, 0.4, -0.2315, -0.1667, 0.4, -0.3, -0.3333, 0.8, 0.3685, -0.1667, 0.8, 0.3, -0.1667, 1, 0.3, -0.1667, 1, 0.3, -0.3333, 1, 0.3685, -0.3333, 0.8, 0.3685, -0.1667, 0.2, -0.5, -0.1667, 0, -0.5, 0, 0, -0.4315, 0, 0, -0.4315, 0, 0.2, -0.4315, -0.1667, 0.2, -0.5, 0, 0.4, -0.0315, 0.1667, 0.4, -0.1, 0.1667, 0.6, -0.1, 0.1667, 0.6, -0.1, 0, 0.6, -0.0315, 0, 0.4, -0.0315, 0.3333, 0.6, 0.1685, 0.5, 0.6, 0.1, 0.5, 0.8, 0.1, 0.5, 0.8, 0.1, 0.3333, 0.8, 0.1685, 0.3333, 0.6, 0.1685, -0.3333, 0.8, 0.3685, -0.5, 0.8, 0.3, -0.5, 0.8, 0.1, -0.5, 0.8, 0.1, -0.3333, 0.8, 0.1685, -0.3333, 0.8, 0.3685, -0.3333, 0.8, 0.1685, -0.1667, 0.8, 0.3, -0.3333, 0.8, 0.3685, -0.3333, 0.8, 0.1685, -0.1667, 0.8, 0.1, -0.1667, 0.8, 0.3, -0.1667, 0.8, 0.1, 0, 0.8, 0.3685, -0.1667, 0.8, 0.3, -0.1667, 0.8, 0.1, 0, 0.8, 0.1685, 0, 0.8, 0.3685, 0, 0.8, 0.1685, 0.1667, 0.8, 0.3, 0, 0.8, 0.3685, 0, 0.8, 0.1685, 0.1667, 0.8, 0.1, 0.1667, 0.8, 0.3, 0.1667, 0.8, 0.1, 0.3333, 0.8, 0.3685, 0.1667, 0.8, 0.3, 0.1667, 0.8, 0.1, 0.3333, 0.8, 0.1685, 0.3333, 0.8, 0.3685, 0.3333, 0.8, 0.1685, 0.5, 0.8, 0.3, 0.3333, 0.8, 0.3685, 0.3333, 0.8, 0.1685, 0.5, 0.8, 0.1, 0.5, 0.8, 0.3, -0.5, 1, 0.5, -0.5, 1, 0.3, -0.3333, 1, 0.3685, -0.3333, 1, 0.3685, 0.5, 1, 0.5, -0.5, 1, 0.5, -0.3333, 1, 0.3685, 0, 1, 0.3685, 0.5, 1, 0.5, -0.3333, 1, 0.3685, -0.1667, 1, 0.3, 0, 1, 0.3685, 0, 1, 0.3685, 0.1667, 1, 0.3, 0.5, 1, 0.5, 0.1667, 1, 0.3, 0.3333, 1, 0.3685, 0.5, 1, 0.5, 0.3333, 1, 0.3685, 0.5, 1, 0.3, 0.5, 1, 0.5, -0.3333, 0.4, -0.0315, -0.5, 0.4, -0.1, -0.5, 0.4, -0.3, -0.5, 0.4, -0.3, -0.3333, 0.4, -0.2315, -0.3333, 0.4, -0.0315, -0.3333, 0.4, -0.2315, -0.1667, 0.4, -0.1, -0.3333, 0.4, -0.0315, -0.3333, 0.4, -0.2315, -0.1667, 0.4, -0.3, -0.1667, 0.4, -0.1, -0.1667, 0.4, -0.3, 0, 0.4, -0.0315, -0.1667, 0.4, -0.1, -0.1667, 0.4, -0.3, 0, 0.4, -0.2315, 0, 0.4, -0.0315, 0, 0.4, -0.2315, 0.1667, 0.4, -0.1, 0, 0.4, -0.0315, 0, 0.4, -0.2315, 0.1667, 0.4, -0.3, 0.1667, 0.4, -0.1, 0.1667, 0.4, -0.3, 0.3333, 0.4, -0.0315, 0.1667, 0.4, -0.1, 0.1667, 0.4, -0.3, 0.3333, 0.4, -0.2315, 0.3333, 0.4, -0.0315, 0.3333, 0.4, -0.2315, 0.5, 0.4, -0.1, 0.3333, 0.4, -0.0315, 0.3333, 0.4, -0.2315, 0.5, 0.4, -0.3, 0.5, 0.4, -0.1, 0, 0.8, 0.1685, -0.1667, 0.8, 0.1, -0.1667, 0.75, 0.05, -0.1667, 0.75, 0.05, 0, 0.75, 0.1185, 0, 0.8, 0.1685, -0.3333, 0.6, -0.0315, -0.5, 0.6, -0.1, -0.5, 0.55, -0.15, -0.5, 0.55, -0.15, -0.3333, 0.55, -0.0815, -0.3333, 0.6, -0.0315, 0, 0.95, 0.3185, 0.1667, 0.95, 0.25, 0.1667, 1, 0.3, 0.1667, 1, 0.3, 0, 1, 0.3685, 0, 0.95, 0.3185, -0.3333, 0.6, -0.0315, -0.3333, 0.55, -0.0815, -0.1667, 0.55, -0.15, -0.1667, 0.55, -0.15, -0.1667, 0.6, -0.1, -0.3333, 0.6, -0.0315, 0, 0.55, -0.0815, 0.1667, 0.55, -0.15, 0.1667, 0.6, -0.1, 0.1667, 0.6, -0.1, 0, 0.6, -0.0315, 0, 0.55, -0.0815, 0.3333, 0.4, -0.2315, 0.3333, 0.35, -0.2815, 0.375, 0.35, -0.2986, 0.375, 0.35, -0.2986, 0.4167, 0.375, -0.2908, 0.3333, 0.4, -0.2315, 0.5, 0.4, -0.3, 0.3333, 0.4, -0.2315, 0.4167, 0.375, -0.2908, 0.5, 0.35, -0.35, 0.5, 0.4, -0.3, 0.4167, 0.375, -0.2908, 0.4167, 0.375, -0.2908, 0.4583, 0.35, -0.3329, 0.5, 0.35, -0.35, -0.3333, 0.2, -0.2315, -0.5, 0.2, -0.3, -0.5, 0.2, -0.5, -0.5, 0.2, -0.5, -0.3333, 0.2, -0.4315, -0.3333, 0.2, -0.2315, -0.3333, 0.2, -0.4315, -0.1667, 0.2, -0.3, -0.3333, 0.2, -0.2315, -0.3333, 0.2, -0.4315, -0.1667, 0.2, -0.5, -0.1667, 0.2, -0.3, -0.1667, 0.2, -0.5, 0, 0.2, -0.2315, -0.1667, 0.2, -0.3, -0.1667, 0.2, -0.5, 0, 0.2, -0.4315, 0, 0.2, -0.2315, 0, 0.2, -0.4315, 0.1667, 0.2, -0.3, 0, 0.2, -0.2315, 0, 0.2, -0.4315, 0.1667, 0.2, -0.5, 0.1667, 0.2, -0.3, 0.1667, 0.2, -0.5, 0.3333, 0.2, -0.2315, 0.1667, 0.2, -0.3, 0.1667, 0.2, -0.5, 0.3333, 0.2, -0.4315, 0.3333, 0.2, -0.2315, 0.3333, 0.2, -0.4315, 0.5, 0.2, -0.3, 0.3333, 0.2, -0.2315, 0.3333, 0.2, -0.4315, 0.5, 0.2, -0.5, 0.5, 0.2, -0.3, 0.3333, 1, 0.3685, 0.3333, 0.95, 0.3185, 0.5, 0.95, 0.25, 0.5, 0.95, 0.25, 0.5, 1, 0.3, 0.3333, 1, 0.3685, -0.3333, 1, 0.3685, -0.5, 1, 0.3, -0.5, 0.95, 0.25, -0.5, 0.95, 0.25, -0.3333, 0.95, 0.3185, -0.3333, 1, 0.3685, -0.3333, 0.4, -0.2315, -0.5, 0.4, -0.3, -0.5, 0.35, -0.35, -0.5, 0.35, -0.35, -0.3333, 0.35, -0.2815, -0.3333, 0.4, -0.2315, 0.3333, 0.4, -0.2315, 0.1667, 0.4, -0.3, 0.1667, 0.35, -0.35, 0.1667, 0.35, -0.35, 0.3333, 0.35, -0.2815, 0.3333, 0.4, -0.2315, 0, 0.35, -0.2815, 0.1667, 0.35, -0.35, 0.1667, 0.4, -0.3, 0.1667, 0.4, -0.3, 0, 0.4, -0.2315, 0, 0.35, -0.2815, 0, 0.2, -0.4315, -0.1667, 0.2, -0.5, -0.1667, 0.15, -0.55, -0.1667, 0.15, -0.55, 0, 0.15, -0.4815, 0, 0.2, -0.4315, 0, 0.15, -0.4815, 0.1667, 0.15, -0.55, 0.1667, 0.2, -0.5, 0.1667, 0.2, -0.5, 0, 0.2, -0.4315, 0, 0.15, -0.4815, 0.3333, 0.2, -0.4315, 0.1667, 0.2, -0.5, 0.1667, 0.15, -0.55, 0.1667, 0.15, -0.55, 0.3333, 0.15, -0.4815, 0.3333, 0.2, -0.4315, -0.3333, 0.2, -0.4315, -0.3333, 0.15, -0.4815, -0.2917, 0.15, -0.4986, -0.2917, 0.15, -0.4986, -0.25, 0.175, -0.4908, -0.3333, 0.2, -0.4315, -0.1667, 0.2, -0.5, -0.3333, 0.2, -0.4315, -0.25, 0.175, -0.4908, -0.1667, 0.15, -0.55, -0.1667, 0.2, -0.5, -0.25, 0.175, -0.4908, -0.25, 0.175, -0.4908, -0.2083, 0.15, -0.5329, -0.1667, 0.15, -0.55, 0.3333, 0.8, 0.1685, 0.3333, 0.75, 0.1185, 0.5, 0.75, 0.05, 0.5, 0.75, 0.05, 0.5, 0.8, 0.1, 0.3333, 0.8, 0.1685, 0.3333, 0.6, -0.0315, 0.1667, 0.6, -0.1, 0.1667, 0.55, -0.15, 0.1667, 0.55, -0.15, 0.3333, 0.55, -0.0815, 0.3333, 0.6, -0.0315, 0, 0.6, -0.0315, -0.1667, 0.6, -0.1, -0.1667, 0.55, -0.15, -0.1667, 0.55, -0.15, -0.0833, 0.575, -0.0908, 0, 0.6, -0.0315, -0.1667, 0.55, -0.15, -0.125, 0.55, -0.1329, -0.0833, 0.575, -0.0908, -0.0833, 0.575, -0.0908, -0.0417, 0.55, -0.0986, 0, 0.6, -0.0315, -0.0417, 0.55, -0.0986, 0, 0.55, -0.0815, 0, 0.6, -0.0315, 0.3333, 0.2, -0.4315, 0.3333, 0.15, -0.4815, 0.5, 0.15, -0.55, 0.5, 0.15, -0.55, 0.5, 0.2, -0.5, 0.3333, 0.2, -0.4315, -0.3333, 0.2, -0.4315, -0.5, 0.2, -0.5, -0.5, 0.15, -0.55, -0.5, 0.15, -0.55, -0.3333, 0.15, -0.4815, -0.3333, 0.2, -0.4315, 0.3333, 1, 0.3685, 0.1667, 1, 0.3, 0.1667, 0.95, 0.25, 0.1667, 0.95, 0.25, 0.25, 0.975, 0.3092, 0.3333, 1, 0.3685, 0.1667, 0.95, 0.25, 0.2083, 0.95, 0.2671, 0.25, 0.975, 0.3092, 0.25, 0.975, 0.3092, 0.2917, 0.95, 0.3014, 0.3333, 1, 0.3685, 0.2917, 0.95, 0.3014, 0.3333, 0.95, 0.3185, 0.3333, 1, 0.3685, -0.3333, 1, 0.3685, -0.3333, 0.95, 0.3185, -0.1667, 0.95, 0.25, -0.1667, 0.95, 0.25, -0.1667, 1, 0.3, -0.3333, 1, 0.3685, -0.3333, 0.4, -0.2315, -0.3333, 0.35, -0.2815, -0.1667, 0.35, -0.35, -0.1667, 0.35, -0.35, -0.1667, 0.4, -0.3, -0.3333, 0.4, -0.2315, 0.3333, 0.6, -0.0315, 0.3333, 0.55, -0.0815, 0.5, 0.55, -0.15, 0.5, 0.55, -0.15, 0.5, 0.6, -0.1, 0.3333, 0.6, -0.0315, 0, 0.4, -0.2315, -0.1667, 0.4, -0.3, -0.1667, 0.35, -0.35, -0.1667, 0.35, -0.35, 0, 0.35, -0.2815, 0, 0.4, -0.2315, -0.3333, 0.6, 0.1685, -0.5, 0.6, 0.1, -0.5, 0.6, -0.1, -0.5, 0.6, -0.1, -0.3333, 0.6, -0.0315, -0.3333, 0.6, 0.1685, -0.3333, 0.6, -0.0315, -0.1667, 0.6, 0.1, -0.3333, 0.6, 0.1685, -0.3333, 0.6, -0.0315, -0.1667, 0.6, -0.1, -0.1667, 0.6, 0.1, -0.1667, 0.6, -0.1, 0, 0.6, 0.1685, -0.1667, 0.6, 0.1, -0.1667, 0.6, -0.1, 0, 0.6, -0.0315, 0, 0.6, 0.1685, 0, 0.6, -0.0315, 0.1667, 0.6, 0.1, 0, 0.6, 0.1685, 0, 0.6, -0.0315, 0.1667, 0.6, -0.1, 0.1667, 0.6, 0.1, 0.1667, 0.6, -0.1, 0.3333, 0.6, 0.1685, 0.1667, 0.6, 0.1, 0.1667, 0.6, -0.1, 0.3333, 0.6, -0.0315, 0.3333, 0.6, 0.1685, 0.3333, 0.6, -0.0315, 0.5, 0.6, 0.1, 0.3333, 0.6, 0.1685, 0.3333, 0.6, -0.0315, 0.5, 0.6, -0.1, 0.5, 0.6, 0.1, 0, 1, 0.3685, -0.1667, 1, 0.3, -0.1667, 0.95, 0.25, -0.1667, 0.95, 0.25, 0, 0.95, 0.3185, 0, 1, 0.3685, 0, 0.75, 0.1185, 0.1667, 0.75, 0.05, 0.1667, 0.8, 0.1, 0.1667, 0.8, 0.1, 0, 0.8, 0.1685, 0, 0.75, 0.1185, -0.3333, 0.8, 0.1685, -0.5, 0.8, 0.1, -0.5, 0.75, 0.05, -0.5, 0.75, 0.05, -0.3333, 0.75, 0.1185, -0.3333, 0.8, 0.1685, 0.3333, 0.8, 0.1685, 0.1667, 0.8, 0.1, 0.1667, 0.75, 0.05, 0.1667, 0.75, 0.05, 0.3333, 0.75, 0.1185, 0.3333, 0.8, 0.1685, -0.3333, 0.8, 0.1685, -0.3333, 0.75, 0.1185, -0.2917, 0.75, 0.1014, -0.2917, 0.75, 0.1014, -0.25, 0.775, 0.1092, -0.3333, 0.8, 0.1685, -0.1667, 0.8, 0.1, -0.3333, 0.8, 0.1685, -0.25, 0.775, 0.1092, -0.1667, 0.75, 0.05, -0.1667, 0.8, 0.1, -0.25, 0.775, 0.1092, -0.25, 0.775, 0.1092, -0.2083, 0.75, 0.0671, -0.1667, 0.75, 0.05)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_lvdpk"]
resource_name = "dirt"
albedo_color = Color(0.948242, 0.744326, 0.619001, 1)
metallic = 1.0
backlight = Color(0.695503, 0.695503, 0.695503, 1)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_68ua2"]
resource_name = "grass"
albedo_color = Color(0.45234, 0.929502, 0.865877, 1)
metallic = 1.0

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_t8mp3"]
resource_name = "_defaultMat"
metallic = 1.0

[sub_resource type="ArrayMesh" id="ArrayMesh_4atkr"]
_surfaces = [{
"aabb": AABB(-0.382394, -5.41434e-16, -0.385019, 0.764788, 0.88378, 0.770037),
"format": ***********,
"index_count": 306,
"index_data": PackedByteArray("AgAAAAEAAwACAAEAAQAEAAMABwAFAAYABgAIAAcACwAJAAoACgAMAAsADAANAAsADQAOAAsAEQAPABAAEAASABEAFQATABQAFAAWABUAGQAXABgAGAAaABkAHQAbABwAHAAeAB0AIQAfACAAIAAiACEAJQAjACQAJAAmACUAJgAnACUAJgAoACcAKwApACoAKgAsACsALAAtACsALQAuACsAMQAvADAAMAAyADEANQAzADQANAA2ADUANgA3ADUANwA4ADUAOQA2ADQAOgA5ADQAOgA0ADsAOwA8ADoAMAA9AD4APgAyADAAQQA/AEAAQABCAEEAQwApACsAKwBEAEMADQAMAEUARQBGAA0AFQBCAEAAQAATABUARgBFAEcARwBIAEYALgAtAEkASQBKAC4AOAA3AB0AHQAJADgAHQAeAAkAHgBLAAkASwBMAAkATAAKAAkASQAzADUANQBKAEkANQAkAEoAJAAjAEoAGgAYABwAHAAbABoATwBNAE4ATgBQAE8ATgBRAFAAEAAPAD8APwBBABAAQwBEAAMAAwBSAEMAAwAEAFIASABHAFEAUQBOAEgAKgAfACEAIQAsACoACwAOAE0ATQBTAAsAUwAoAAsAKAAmAAsAUwBNAE8AFAARABIAEgAWABQAIgAgAFQAVABVACIAPQBSAAQAPgA9AAQABAABAD4ACAAGAFYAVgBXAAgAPAAFAAcABwA6ADwAOQAXABkAGQA2ADkAAgAlACcAJwAAAAIAVQBUADsAOwA0AFUASwBXAFYAVgBMAEsAMQBTAE8ATwBQADEAUAAvADEA"),
"name": "dirt",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 88,
"vertex_data": PackedByteArray("LsAHXTBmAABfyQddMGYAAMzEGINobwAAlM4Yg2hvAADf2f44dV0AAMRYAABG/QAAlh0AAEb9AADZUioyC/AAAIEjKjIL8AAAmCassPCJAACqEgAA8IkAAOtJrLArTQAAdRgAAPp/AACLM/RHWF8AAANE9EcCQwAAB3yssIO2AACHdv//Da0AAGSQrLB7kwAAZYX//3uTAABMU6ywcnAAAAd8rLBycAAAy1j//+h5AACHdv//6HkAABBMKjLh4AAASioqMuHgAADbSJlqtdkAAH8tmWq12QAA0R+ZaiG7AABmGSoyIbsAAMMrmWptoAAAXiUqMmCgAAA24QAAztEAAHmlAAD//wAAHMU1M0u/AABinjUzPN0AAAm5GIPwiQAA5bOssPCJAABhtRiDp4MAAJGQrLArTQAAx7IHXcl3AABJlQddDkUAAP//AAANsgAA6ucAAPa4AAAG4BiDLZoAANHHNTNDtQAAILM1MzO7AABesBiD2acAADF4AACpCgAAIsoAAKkKAADxhgddCSQAAGK7B10JJAAApZc1M+DSAABnkzUz4NIAAJGQrLC0xgAAUE6ZaoHNAAD3RZlqgc0AAOtJrLC0xgAASFIqMvrSAADZXyoy+tIAAM9zAADY1wAAgmkAANjXAAAb8wAAJFEAAJvVB10kUQAATFOssIO2AADvPqywe5MAAMtY//8NrQAA7kn//3uTAAD//wAAoYsAAAbgGIP0kgAAegcAADdkAADKMfRHelwAAJAYAADYBwAApDr0R54sAABopTUzM7sAAKWnGIPZpwAAsx0qMjCTAACNFAAALo0AADpW9EcCQwAAOk30R8kqAACeZqoqK0AAANhwAABMFwAAMGgAAAAAAACI6gAA5V8AAL9zB10ORQAAXHkAAEH0AADwlDUzudoAAAAAAAAhuwAA1QsqMiG7AAA=")
}, {
"aabb": AABB(-0.347036, 0.173184, -0.276622, 0.633919, 0.710596, 0.613659),
"format": ***********,
"index_count": 150,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAQAEAAMABAAFAAMABQAGAAMABAABAAcACgAIAAkACQALAAoACwAMAAoACwANAAwADQAOAAwADgAPAAwACQAIABAAEAARAAkAFAASABMAEwAVABQAFQAWABQAFQAXABYAFwAYABYAEwASABkAGQAaABMAGQAbABoAGwAWABgAGwAcABoAGwAYABwAGwAdABYAIAAeAB8AHwAhACAAIQAiACAAIQAjACIAIQAkACMAJAAlACMAKAAmACcAJwApACgAJwAqACkAKgArACkALgAsAC0ALQAvAC4ALwAwAC4ALwAxADAANAAyADMAMwA1ADQAMwA2ADUANgA3ADUANgA4ADcAOAA5ADcAPAA6ADsAOwA9ADwAOwA+AD0APgA/AD0A"),
"name": "grass",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 64,
"vertex_data": PackedByteArray("+ruoZGelAAD00Khk3n8AAH/GqGRnpQAA//+oZD6UAAAl36hklF4AAPLqqGSUXgAA//+oZC+LAACMzKhk+3cAAAAAAACYvQAAXhAAAJi9AACOHAAA//8AAL0kAAD37AAArFUAAP//AAB9TQAA9+wAAP1UAACF2wAAXGUAAIXbAACOFQAAeosAAM4eAAAGnAAASCBXnd5/AAClPVed2IsAAOZKV50fzAAAN1ZXnc23AAAioFedH8wAAFqHV53NtwAA7J9XndiLAADmSlednTMAADdWV53iXwAAIqBXnZ0zAABah1ed4l8AAMHKV53efwAAXX1PNW4pAACFlE81AAAAANOlTzVuKQAAytNPNQAAAABoyU81GGkAAJPZTzUCUwAAbfNPNZo4AACq5E81AlMAAOpK///YiwAA2Vz//8BrAADZXP//76sAALiA///vqwAAuID//8BrAACnkv//2IsAABwYMEaYvQAAhSYwRhecAACcKDBG9+MAACJGMEan1AAAnkkwRvfjAAA0UDBGp9QAAI6jTAFl2wAArKhMAWXbAABopUwBPeUAAM6wTAFl6AAAR7lMAa+9AACG30wB0sIAANPJTAGvvQAAyuJMATy2AADKLRkb00YAAHg4GRvFCgAA5y8ZG2tKAADGQxkb3iYAAOROGRt4CAAAwFkZG94mAAA=")
}, {
"aabb": AABB(-0.382394, -5.41434e-16, -0.385019, 0.764788, 1e-05, 0.770037),
"format": ***********,
"index_count": 60,
"index_data": PackedByteArray("AgAAAAEABAADAAIAAQAFAAYABgACAAEABgAEAAIABgAHAAQABgAIAAcACAAJAAcACQAKAAcACQALAAoACwAMAAoACwANAAwADQAOAAwADQAPAA4ADwAQAA4AEAARAA4AEQASAA4AEAATABEAEQATABQAFAAVABEA"),
"name": "_defaultMat",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 22,
"vertex_data": PackedByteArray("qhIAAPCJAACNFAAALo0AAHUYAAD6fwAAegcAADdkAACQGAAA2AcAAAAAAAAhuwAAlh0AAEb9AAAwaAAAAAAAAMRYAABG/QAAgmkAANjXAADYcAAATBcAAM9zAADY1wAAMXgAAKkKAABceQAAQfQAACLKAACpCgAAeaUAAP//AAA24QAAztEAAIjqAADlXwAAG/MAACRRAADq5wAA9rgAAP//AAANsgAA//8AAKGLAAA=")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_615qo"]
resource_name = "rock_tallB_Mesh rock_tallB"
_surfaces = [{
"aabb": AABB(-0.382394, -5.41434e-16, -0.385019, 0.764788, 0.88378, 0.770037),
"attribute_data": PackedByteArray("YUEqV2k4KlfgPBBDVTMQQ1AoMmq5WW2Q/R9tkPNT1XXDJdV1ELSQK8m9LYcjb5ArfLIth4OJ3WFgad1hr08cKgxV6ABrdxwqDnLoAJuruSnfg7kpPqaFADyJhQBVTdZuYSzWbjNKgFGCL4BRLdt+T7fdxWx5vn5P9sDFbHmZTZ6cT02eAY87gCBfO4C4W/lBOl5XKthi+UEno1cqNm63VcGnt1VR6oyL3tGMi93SUkbKtIFwx5+BcHiiUkachwOQqzcDkDl5m14PRpteEZeTbO6Sk2wqkIUrh0/aT2JH2k89S4UrZlMebaJgHm0bdCGHDmohh7Rvi5AYfiNfpr+LkEKxI1+RmA8s1XAPLDST2gIydtoC1E6dj0hmY0qSdJ2PYG1jSohLBZWgKwWV2z25a5A6uWvyqvIBT7AnK/CN8gGTiCcraHrbmyYe25t4a49yqzuPcuCxaW9/pGlvMK86Ra2mOkWfpUdQf3JHUMSh8SvXXPErVW+KbV9gim3NVo2HH1ONhwdFxmvuS7gq4l/Ga29xWkLbkLgqXZNaQgS1tG5wjLRu2a9uUfuOblFOxtZeq9/WXmTDpm+Z5SKI/v0iiB98iitjVIorwnZVAsBZVQI1RrhIDm24SAI/8o3NbvKNEnjZb8LYOJ2yijidPrvscwWp7HPsSkmcZFU2fjBkSZyYXzZ+hLoQYb+oEGHBtMQq8osjVjprI1bUb8QqwphAcG1nNCnKbAAAKY80KcyJAADbwzGcTZcxnFa0Hn7Mqh5+Rs+qizjJZW0B4KqLndFCWr+5Qlp5yV+PYIJfj/G/AnUQhwJ1AAAPhYcJsWo7KA+FxiixanQUq2mfGWRMZiOrab4mZExyY2M9rlt9UYNKYz0BRn1R3+7siXHS7Ilg29lrhtPZaxfjC3HH52iLKbgLcWS2aIvUUzJZFFbyc2EuMllSLpqK/R+aig=="),
"format": ***********,
"index_count": 306,
"index_data": PackedByteArray("AgAAAAEAAwACAAEAAQAEAAMABwAFAAYABgAIAAcACwAJAAoACgAMAAsADAANAAsADQAOAAsAEQAPABAAEAASABEAFQATABQAFAAWABUAGQAXABgAGAAaABkAHQAbABwAHAAeAB0AIQAfACAAIAAiACEAJQAjACQAJAAmACUAJgAnACUAJgAoACcAKwApACoAKgAsACsALAAtACsALQAuACsAMQAvADAAMAAyADEANQAzADQANAA2ADUANgA3ADUANwA4ADUAOQA2ADQAOgA5ADQAOgA0ADsAOwA8ADoAPwA9AD4APgBAAD8AQwBBAEIAQgBEAEMARwBFAEYARgBIAEcASwBJAEoASgBMAEsATwBNAE4ATgBQAE8AUwBRAFIAUgBUAFMAVwBVAFYAVgBYAFcAWwBZAFoAWgBcAFsAWgBdAFwAXQBeAFwAXgBfAFwAXwBgAFwAYwBhAGIAYgBkAGMAYgBlAGQAZQBmAGQAaQBnAGgAaABqAGkAbQBrAGwAbABuAG0AbABvAG4AcgBwAHEAcQBzAHIAdgB0AHUAdQB3AHYAdQB4AHcAewB5AHoAegB8AHsAfwB9AH4AfgCAAH8AgwCBAIIAggCEAIMAhACFAIMAhQCGAIMAhACCAIcAigCIAIkAiQCLAIoAjgCMAI0AjQCPAI4AkgCQAJEAkwCSAJEAkQCUAJMAlwCVAJYAlgCYAJcAmwCZAJoAmgCcAJsAnwCdAJ4AngCgAJ8AowChAKIAogCkAKMApwClAKYApgCoAKcAqwCpAKoAqgCsAKsArwCtAK4ArgCwAK8AsACxAK8A"),
"material": SubResource("StandardMaterial3D_lvdpk"),
"name": "dirt",
"primitive": 3,
"uv_scale": Vector4(30.863, 67.3412, 0, 0),
"vertex_count": 178,
"vertex_data": PackedByteArray("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")
}, {
"aabb": AABB(-0.347036, 0.173184, -0.276622, 0.633919, 0.710596, 0.613659),
"attribute_data": PackedByteArray("r6tNs9i+h5NLtU2zz+nHpM/LWHeV1lh3z+kbndK62owAAMjH8g7IxxUa//+OIePvP07//8ZG4++gTR7hk1we4bATWp0iHF2rfB2Hk004qp1oRBTUvk7hwkKSFNSfe+HCEJKqnWhE+VK+TnN4QpL5Up97c3guuYeTgHJaSqaHRid0l1pKb8FGJ/O3P4C4xoxtVN4xV9nQjG1rRKqdzVR/gs1U1LiQddS4kHV/gvKFqp0FFsjHLiNrqxclROgOQE3bPENE6EBJTdthlQLhDZoC4RKXWOl7oQTsOKnbxybMNcxVuNvHIs+NwdIpPGOTM2QwwCtHZuY9LkgOSHIu+FEuSA=="),
"format": ***********,
"index_count": 150,
"index_data": PackedByteArray("AgAAAAEAAQADAAIAAQAEAAMABAAFAAMABQAGAAMABAABAAcACgAIAAkACQALAAoACwAMAAoACwANAAwADQAOAAwADgAPAAwACQAIABAAEAARAAkAFAASABMAEwAVABQAFQAWABQAFQAXABYAFwAYABYAEwASABkAGQAaABMAGQAbABoAGwAWABgAGwAcABoAGwAYABwAGwAdABYAIAAeAB8AHwAhACAAIQAiACAAIQAjACIAIQAkACMAJAAlACMAKAAmACcAJwApACgAJwAqACkAKgArACkALgAsAC0ALQAvAC4ALwAwAC4ALwAxADAANAAyADMAMwA1ADQAMwA2ADUANgA3ADUANgA4ADcAOAA5ADcAPAA6ADsAOwA9ADwAOwA+AD0APgA/AD0A"),
"material": SubResource("StandardMaterial3D_68ua2"),
"name": "grass",
"primitive": 3,
"uv_scale": Vector4(27.3256, 28.5384, 0, 0),
"vertex_count": 64,
"vertex_data": PackedByteArray("+ruoZGel/7/00Khk3n//v3/GqGRnpf+///+oZD6U/78l36hklF7/v/LqqGSUXv+///+oZC+L/7+MzKhk+3f/vwAAAACYvf+/XhAAAJi9/7+OHAAA////v70kAAD37P+/rFUAAP///799TQAA9+z/v/1UAACF2/+/XGUAAIXb/7+OFQAAeov/v84eAAAGnP+/SCBXnd5//7+lPVed2Iv/v+ZKV50fzP+/N1ZXnc23/78ioFedH8z/v1qHV53Nt/+/7J9XndiL/7/mSlednTP/vzdWV53iX/+/IqBXnZ0z/79ah1ed4l//v8HKV53ef/+/XX1PNW4p/7+FlE81AAD/v9OlTzVuKf+/ytNPNQAA/79oyU81GGn/v5PZTzUCU/+/bfNPNZo4/7+q5E81AlP/v+pK///Yi/+/2Vz//8Br/7/ZXP//76v/v7iA///vq/+/uID//8Br/7+nkv//2Iv/vxwYMEaYvf+/hSYwRhec/7+cKDBG9+P/vyJGMEan1P+/nkkwRvfj/780UDBGp9T/v46jTAFl2/+/rKhMAWXb/79opUwBPeX/v86wTAFl6P+/R7lMAa+9/7+G30wB0sL/v9PJTAGvvf+/yuJMATy2/7/KLRkb00b/v3g4GRvFCv+/5y8ZG2tK/7/GQxkb3ib/v+ROGRt4CP+/wFkZG94m/7////9/////f////3////9/////f////3////9/////f////3////9/////f////3////9//v//f////3////9//v//f/7//3////9/////f////3////9/////f////3////9/////f////3////9/////f////3////9/////f////3////9/////f////3////9/////f////3////9/////f////3////9/////f/7//3////9/////f////3////9/////f////3/+//9//v//f/7//3////9/////f////3////9//v//f////3/+//9/////f////3////9/")
}, {
"aabb": AABB(-0.382394, -5.41434e-16, -0.385019, 0.764788, 1e-05, 0.770037),
"attribute_data": PackedByteArray("//QAAP//AAB68xG///QAAP//AAB68xG///QAAP//AAB68xG///QAAP//AAB68xG///QAAP//AAB68xG///QAAP//AAB68xG///QAAP//AAB68xG///QAAP//AAB68xG///QAAP//AAB68xG///QAAP//AAB68xG///QAAP//AAB68xG///QAAP//AAB68xG///QAAP//AAB68xG///QAAP//AAB68xG///QAAP//AAB68xG///QAAP//AAB68xG///QAAP//AAB68xG///QAAP//AAB68xG///QAAP//AAB68xG///QAAP//AAB68xG/"),
"format": ***********,
"index_count": 60,
"index_data": PackedByteArray("AgAAAAEABQADAAQACAAGAAcACwAJAAoADgAMAA0AEQAPABAAFAASABMAFwAVABYAGgAYABkAHQAbABwAIAAeAB8AIwAhACIAJgAkACUAKQAnACgALAAqACsALwAtAC4AMgAwADEANQAzADQAOAA2ADcAOwA5ADoA"),
"material": SubResource("StandardMaterial3D_t8mp3"),
"name": "_defaultMat",
"primitive": 3,
"uv_scale": Vector4(22.0333, 10.5995, 0, 0),
"vertex_count": 60,
"vertex_data": PackedByteArray("qhIAAPCJSsqNFAAALo1KynUYAAD6f0rKegcAADdk48l1GAAA+n/jyZAYAADYB+PJAAAAACG7SMyWHQAARv1IzI0UAAAujUjMdRgAAPp/VtyNFAAALo1W3JYdAABG/VbckBgAANgHWdV1GAAA+n9Z1ZYdAABG/VnVMGgAAAAAJf2QGAAA2Acl/ZYdAABG/SX9xFgAAEb97NMwaAAAAADs05YdAABG/ezTgmkAANjXedUwaAAAAAB51cRYAABG/XnV2HAAAEwXFN4waAAAAAAU3oJpAADY1xTez3MAANjXsNXYcAAATBew1YJpAADY17DVMXgAAKkKjOLYcAAATBeM4s9zAADY14ziXHkAAEH0ctUxeAAAqQpy1c9zAADY13LVIsoAAKkK//8xeAAAqQr//1x5AABB9P//eaUAAP//9NEiygAAqQr00Vx5AABB9PTRNuEAAM7RE9giygAAqQoT2HmlAAD//xPYiOoAAOVfRt4iygAAqQpG3jbhAADO0UbeG/MAACRRjOIiygAAqQqM4ojqAADlX4zi6ucAAPa4pdSI6gAA5V+l1DbhAADO0aXU6ucAAPa4zMD//wAADbLMwIjqAADlX8zA//8AAKGLseCI6gAA5V+x4P//AAANsrHgTCKXxEwil8RMIpfE4iHDw+Ihw8PiIcPDLCRXyCwkV8gsJFfIkS4i3ZEuIt2RLiLdrSpa1a0qWtWtKlrVdD7n/HQ+5/x0Puf8mFMzVphTM1aYUzNWgVU+VYFVPlWBVT5V7F6JUOxeiVDsXolQw1UdVcNVHVXDVR1VqTFS46kxUuOpMVLjeVVCVXlVQlV5VUJV/3//P/9//z//f/8/B1F7VwdRe1cHUXtXiFi7U4hYu1OIWLtTHV9wUB1fcFAdX3BQU2NVTlNjVU5TY1VOf1S/VX9Uv1V/VL9VPRzgcT0c4HE9HOBxhWE8T4VhPE+FYTxP")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_4atkr")

[sub_resource type="BoxShape3D" id="BoxShape3D_l1bbt"]
size = Vector3(25.4455, 0.506836, 21.1632)

[sub_resource type="BoxShape3D" id="BoxShape3D_2140p"]
size = Vector3(13.8471, 0.506836, 11.0851)

[sub_resource type="BoxShape3D" id="BoxShape3D_q4s3c"]
size = Vector3(12.505, 0.427734, 22.3577)

[sub_resource type="BoxShape3D" id="BoxShape3D_3p1xs"]
size = Vector3(25.5963, 0.219238, 2.37329)

[sub_resource type="BoxShape3D" id="BoxShape3D_fmjjh"]
size = Vector3(25.9758, 0.506836, 19.8373)

[sub_resource type="BoxShape3D" id="BoxShape3D_yhwsg"]
size = Vector3(12.9962, 0.506836, 10.6307)

[sub_resource type="BoxShape3D" id="BoxShape3D_qmho6"]
size = Vector3(3.31003, 0.036377, 1.33803)

[sub_resource type="BoxShape3D" id="BoxShape3D_7q62w"]
size = Vector3(3.21228, 0.125977, 10.7733)

[sub_resource type="BoxShape3D" id="BoxShape3D_5bci7"]
size = Vector3(0.427979, 0.575684, 11.9736)

[sub_resource type="ShaderMaterial" id="ShaderMaterial_507rw"]
render_priority = 0
shader = ExtResource("92_eexyd")
shader_parameter/ColorParameter = Color(0, 0.526231, 0.825762, 1)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_03lo3"]

[sub_resource type="ArrayMesh" id="ArrayMesh_g6yur"]
_surfaces = [{
"aabb": AABB(-0.387903, -0.000316895, -0.461798, 0.775806, 1.19343, 1.02993),
"format": ***********,
"index_count": 1536,
"index_data": PackedByteArray("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"),
"lods": [0.0177494, PackedByteArray("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"), 0.0275848, PackedByteArray("HwFvAAwAHwEMAG4ADQBvAB8BHwFuAAsAewANAB8BfgAfAQsAewAfAX4ACQENAHsACQFqAA0AdgBqAAkBdgAEAGoAdgAuAAQAdwB2AAkBdwAuAHYACgEJAXsAdwAJAQoBCgF7AHoAegB7AH4AaQB3AAoBaQAKAXoAdwCqAC4AaQCqAHcAqgAvAC4AegB+AH0AqgBeAC8ALwBeADAAMACeACMAMAAlAJ4AXgAlADAAXgCfACUAXgAmAJ8AXgCgACYAXgAnAKAAXgCdACcAXgAkAJ0AXgDhACQAXgBWAOEAXgDkAFYAXgBfAOQA5ABfAFUAVQBfAOMAXwBUAOMAXwDiAFQAXwBTAOIAqgDlAF4A5QBdAF4AaQDlAKoAuwBdAOUAuwA3AF0AuwCuADcAQAC7AOUAaQBAAOUAuwDxAK4AuwBAAPEA8QA9AK4AQAC/APEA8QC/AD0AaQC9AEAAQAD1AL8AQAC9APUAvwAHAT0APQAHAbMABwE8ALMABwGyADwAvwD0AAcB9QD0AL8ABwE7ALIA9ADCAAcBwgA7AAcB9QDCAPQAwgDXADsA9QD4AMIA+ADXAMIA+QD4APUAvQD5APUA+AD7ANcA+QD7APgAvQAOAfkAvQBpAA4BaQB6AA4BegB9AA4BDgF9ABABfQB+ABAB+QAOARQB+QAUAfwA+QD8APsADgETARQBDgEQARMBEAF+AJMAEAGTABMBfgALAJMA/AAUAYgAFAETAYgA/ACIAAcAiAATASAABwDMAPwAEwGJACAA/ADMAFAA/ABQAPsAEwEhAIkA+wBQAM0AEwGTACEA+wDNAFEAIQCTACIAIQAiAIoAkwB1ACIA+wBRANcAUQBSANcAUQDOAFIA1wBSALkAdQBrAAgAdQAJAGsAkwBzAHUAcwAJAHUAkwALAHMAcwBsAAkACwBtAHMAcwAKAGwAcwBtAAoAuQA4AK8AuQCvADkAtwC5ADkA1wC5ALcAtwA5ALAA1wC3ADsAtwCwADoAOwC3ALEAtwA6ALEA"), 0.350811, PackedByteArray("fQBvAAwADQBvAH0AfQAMAG4AfQBuAAsAagANAH0AfQALAIkACwBtAIkAagB9AIkAiQBtAAoAagAuAAQAiQAKAGwAiQBsAAkAiQAJAHUAdQAJAGsAdQBrAAgAiQB1ACIAIQCJACIAIQAiAIoAiACJACAABwCJAIgABwBqAIkABwDMAFAABwBQAGoAagBQAM0AagDNAFEAUQDOAFIAUQBSALkAuQA4AK8AuQCvADkAUQC5ADkAUQA5ALAAUQCwADoAUQA6ALEAOwBRALEAagBRADsAagA7ALIAagCyADwAagA8ALMAPQBqALMArgBqAD0AXQBqAK4AagBdAC4ArgA3AF0ALgBdAF4ALgBeAC8ALwBeADAAMACeACMAMAAlAJ4AXgAlADAAXgCfACUAXgAmAJ8AXgCgACYAXgAnAKAAXgCdACcAXgAkAJ0AXgDhACQAXgBWAOEAXgDkAFYAXgBfAOQA5ABfAFUAVQBfAOMAXwBUAOMAXwDiAFQAXwBTAOIA")],
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 289,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_tecrg"]
resource_name = "waterfall_scene_Plane"
_surfaces = [{
"aabb": AABB(-0.387903, -0.000316895, -0.461798, 0.775806, 1.19343, 1.02993),
"attribute_data": PackedByteArray("n8CUzJ/ASeafwLm/n8Bu2f//3rKfwN6yn8Ak85/A//////////8k8///Seb//27Z//+UzP//ub8n8N6yT+DesnfQ3rJ30Lm/T+C5vyfwub930JTMT+CUzCfwlMx30G7ZT+Bu2Sfwbtl30EnmT+BJ5ifwSeZ30CTzT+Ak8yfwJPN30P//T+D//yfw/////3R/n8B0fyfwdH9P4HR/d9B0f0/gBKZP4CmZT+BOjHfQTox30CmZd9AEpv//BKb//ymZ//9OjCfwTown8CmZJ/AEpp/AToyfwCmZn8AEpkCB3rJAgf//QIEk80CBSeZAgW7ZQIGUzECBub8Ykd6y8KDessiw3rLIsLm/8KC5vxiRub/IsJTM8KCUzBiRlMzIsG7Z8KBu2RiRbtnIsEnm8KBJ5hiRSebIsCTz8KAk8xiRJPPIsP//8KD//xiR//9AgXR/GJF0f/CgdH/IsHR/8KAEpvCgKZnwoE6MyLBOjMiwKZnIsASmQIEEpkCBKZlAgU6MGJFOjBiRKZkYkQSmn8AB05/AkfmfwLbsn8Dc35/AJsafwEy5i8jesv//TLn//5H5//+27P//3N///wHT//8mxhP4ub8T+JTME/hu2RP4SeYT+CTzE/j//xP43rI76N6yY9jesovIub9j2Lm/O+i5v4vIlMxj2JTMO+iUzIvIbtlj2G7ZO+hu2YvISeZj2EnmO+hJ5ovIJPNj2CTzO+gk84vI//9j2P//O+j//3fQkflP4JH5J/CR+XfQtuxP4LbsJ/C27HfQ3N9P4NzfJ/Dc33fQAdNP4AHTJ/AB03fQJsZP4CbGJ/AmxnfQTLlP4Ey5J/BMuYvIdH8T+HR/O+h0f2PYdH876ASmO+gpmTvoToyLyASmi8gpmYvITowT+ASmE/gpmRP4Toxj2ASmY9gpmWPYToyzuN6yQIFMuUCBkflAgbbsQIHc30CBAdNAgSbGLIm5vyyJlMwsiW7ZLIlJ5iyJJPMsif//LInesgSZ3rLcqN6ys7i5v9youb8Embm/s7iUzNyolMwEmZTMs7hu2dyobtkEmW7Zs7hJ5tyoSeYEmUnms7gk89yoJPMEmSTzs7j//9yo//8Emf//yLCR+fCgkfkYkZH5yLC27PCgtuwYkbbsyLDc3/Cg3N8YkdzfyLAB0/CgAdMYkQHTyLAmxvCgJsYYkSbGyLBMufCgTLkYkUy5s7h0fyyJdH8EmXR/3Kh0fwSZBKYEmSmZBJlOjLO4BKazuCmZs7hOjCyJBKYsiSmZLIlOjNyoBKbcqCmZ3KhOjASZTLncqEy5s7hMuQSZJsbcqCbGs7gmxgSZAdPcqAHTs7gB0wSZ3N/cqNzfs7jc3wSZtuzcqLbss7i27ASZkfncqJH5s7iR+SyJkfksibbsLInc3yyJAdMsiSbGLIlMuTvoTLlj2Ey5i8hMuTvoJsZj2CbGi8gmxjvoAdNj2AHTi8gB0zvo3N9j2Nzfi8jc3zvotuxj2Lbsi8i27Dvokflj2JH5i8iR+RP4kfkT+LbsE/jc3xP4AdMT+CbGE/hMuUCBbtlAgZTMQIG5v///TLn//0y5QIFMuUCBAdNAgSbG"),
"format": ***********,
"index_count": 1536,
"index_data": PackedByteArray("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"),
"lods": [0.0177494, PackedByteArray("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"), 0.0275848, PackedByteArray("HwFvAAwAHwEMAG4ADQBvAB8BHwFuAAsAewANAB8BfgAfAQsAewAfAX4ACQENAHsACQFqAA0AdgBqAAkBdgAEAGoAdgAuAAQAdwB2AAkBdwAuAHYACgEJAXsAdwAJAQoBCgF7AHoAegB7AH4AaQB3AAoBaQAKAXoAdwCqAC4AaQCqAHcAqgAvAC4AegB+AH0AqgBeAC8ALwBeADAAMACeACMAMAAlAJ4AXgAlADAAXgCfACUAXgAmAJ8AXgCgACYAXgAnAKAAXgCdACcAXgAkAJ0AXgDhACQAXgBWAOEAXgDkAFYAXgBfAOQA5ABfAFUAVQBfAOMAXwBUAOMAXwDiAFQAXwBTAOIAqgDlAF4A5QBdAF4AaQDlAKoAuwBdAOUAuwA3AF0AuwCuADcAQAC7AOUAaQBAAOUAuwDxAK4AuwBAAPEA8QA9AK4AQAC/APEA8QC/AD0AaQC9AEAAQAD1AL8AQAC9APUAvwAHAT0APQAHAbMABwE8ALMABwGyADwAvwD0AAcB9QD0AL8ABwE7ALIA9ADCAAcBwgA7AAcB9QDCAPQAwgDXADsA9QD4AMIA+ADXAMIA+QD4APUAvQD5APUA+AD7ANcA+QD7APgAvQAOAfkAvQBpAA4BaQB6AA4BegB9AA4BDgF9ABABfQB+ABAB+QAOARQB+QAUAfwA+QD8APsADgETARQBDgEQARMBEAF+AJMAEAGTABMBfgALAJMA/AAUAYgAFAETAYgA/ACIAAcAiAATASAABwDMAPwAEwGJACAA/ADMAFAA/ABQAPsAEwEhAIkA+wBQAM0AEwGTACEA+wDNAFEAIQCTACIAIQAiAIoAkwB1ACIA+wBRANcAUQBSANcAUQDOAFIA1wBSALkAdQBrAAgAdQAJAGsAkwBzAHUAcwAJAHUAkwALAHMAcwBsAAkACwBtAHMAcwAKAGwAcwBtAAoAuQA4AK8AuQCvADkAtwC5ADkA1wC5ALcAtwA5ALAA1wC3ADsAtwCwADoAOwC3ALEAtwA6ALEA"), 0.350811, PackedByteArray("fQBvAAwADQBvAH0AfQAMAG4AfQBuAAsAJAENAH0AfQALAIkACwBtAIkAagB9AIkAiQBtAAoAagAuAAQAiQAKAGwAiQBsAAkAiQAJAHUAdQAJAGsAdQBrAAgAiQB1ACIAIQCJACIAIQAiAIoAiACJACAABwCJAIgABwAkAYkABwDMAFAABwBQACQBJAFQAM0AJAHNAFEAUQDOAFIAUQBSALkAuQA4AK8AuQCvADkAUQC5ADkAUQA5ALAAUQCwADoAUQA6ALEAIQFRALEAJAFRACEBJAEhAScBJAEnASIBJAEiASgBIwEkASgBJgEkASMBXQAlASYBJQFdAC4ArgA3AF0ALgBdAF4ALgBeAC8ALwBeADAAMACeACMAMAAlAJ4AXgAlADAAXgCfACUAXgAmAJ8AXgCgACYAXgAnAKAAXgCdACcAXgAkAJ0AXgDhACQAXgBWAOEAXgDkAFYAXgBfAOQA5ABfAFUAVQBfAOMAXwBUAOMAXwDiAFQAXwBTAOIA")],
"material": SubResource("StandardMaterial3D_03lo3"),
"primitive": 3,
"uv_scale": Vector4(1.96714, 1.99073, 0, 0),
"vertex_count": 297,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_g6yur")

[sub_resource type="Curve" id="Curve_032nn"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(0.442029, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="CurveTexture_cccmc"]
curve = SubResource("Curve_032nn")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_6dksy"]
lifetime_randomness = 0.5
emission_shape_scale = Vector3(0.46, 0, 0.245)
emission_shape = 2
emission_sphere_radius = 2.0
direction = Vector3(0, 0, 0.5)
spread = 50.0
initial_velocity_min = 0.1
initial_velocity_max = 0.5
gravity = Vector3(0, -1, 0)
damping_min = 0.5
damping_max = 1.0
scale_min = 3.0
scale_max = 5.0
scale_curve = SubResource("CurveTexture_cccmc")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_qnpft"]
emission_enabled = true
emission = Color(1, 1, 1, 1)
emission_energy_multiplier = 0.5

[sub_resource type="SphereMesh" id="SphereMesh_hlix5"]
material = SubResource("StandardMaterial3D_qnpft")
radius = 0.1
height = 0.2

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_bxal7"]
lifetime_randomness = 1.0
emission_shape_scale = Vector3(0.25, 0.25, 0.515)
emission_shape = 3
emission_box_extents = Vector3(1, 1, 1)
velocity_pivot = Vector3(0, 1, 0)
direction = Vector3(0, 1, 0)
spread = 50.0
initial_velocity_max = 3.0
gravity = Vector3(0, -30, 0)
attractor_interaction_enabled = false
scale_min = 0.13
scale_max = 1.5
scale_curve = SubResource("CurveTexture_cccmc")

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_rquir"]
transparency = 1
shading_mode = 0
albedo_texture = ExtResource("93_358ou")
billboard_mode = 3
billboard_keep_scale = true
particles_anim_h_frames = 1
particles_anim_v_frames = 1
particles_anim_loop = false

[sub_resource type="QuadMesh" id="QuadMesh_1uiyl"]
material = SubResource("StandardMaterial3D_rquir")
size = Vector2(0.25, 0.25)

[sub_resource type="ArrayMesh" id="ArrayMesh_s4xlb"]
resource_name = "waterfall_scene_Plane"
_surfaces = [{
"aabb": AABB(-0.387903, -0.000316895, -0.461798, 0.775806, 1.19343, 1.02993),
"attribute_data": PackedByteArray("n8CUzJ/ASeafwLm/n8Bu2f//3rKfwN6yn8Ak85/A//////////8k8///Seb//27Z//+UzP//ub8n8N6yT+DesnfQ3rJ30Lm/T+C5vyfwub930JTMT+CUzCfwlMx30G7ZT+Bu2Sfwbtl30EnmT+BJ5ifwSeZ30CTzT+Ak8yfwJPN30P//T+D//yfw/////3R/n8B0fyfwdH9P4HR/d9B0f0/gBKZP4CmZT+BOjHfQTox30CmZd9AEpv//BKb//ymZ//9OjCfwTown8CmZJ/AEpp/AToyfwCmZn8AEpkCB3rJAgf//QIEk80CBSeZAgW7ZQIGUzECBub8Ykd6y8KDessiw3rLIsLm/8KC5vxiRub/IsJTM8KCUzBiRlMzIsG7Z8KBu2RiRbtnIsEnm8KBJ5hiRSebIsCTz8KAk8xiRJPPIsP//8KD//xiR//9AgXR/GJF0f/CgdH/IsHR/8KAEpvCgKZnwoE6MyLBOjMiwKZnIsASmQIEEpkCBKZlAgU6MGJFOjBiRKZkYkQSmn8AB05/AkfmfwLbsn8Dc35/AJsafwEy5i8jesv//TLn//5H5//+27P//3N///wHT//8mxhP4ub8T+JTME/hu2RP4SeYT+CTzE/j//xP43rI76N6yY9jesovIub9j2Lm/O+i5v4vIlMxj2JTMO+iUzIvIbtlj2G7ZO+hu2YvISeZj2EnmO+hJ5ovIJPNj2CTzO+gk84vI//9j2P//O+j//3fQkflP4JH5J/CR+XfQtuxP4LbsJ/C27HfQ3N9P4NzfJ/Dc33fQAdNP4AHTJ/AB03fQJsZP4CbGJ/AmxnfQTLlP4Ey5J/BMuYvIdH8T+HR/O+h0f2PYdH876ASmO+gpmTvoToyLyASmi8gpmYvITowT+ASmE/gpmRP4Toxj2ASmY9gpmWPYToyzuN6yQIFMuUCBkflAgbbsQIHc30CBAdNAgSbGLIm5vyyJlMwsiW7ZLIlJ5iyJJPMsif//LInesgSZ3rLcqN6ys7i5v9youb8Embm/s7iUzNyolMwEmZTMs7hu2dyobtkEmW7Zs7hJ5tyoSeYEmUnms7gk89yoJPMEmSTzs7j//9yo//8Emf//yLCR+fCgkfkYkZH5yLC27PCgtuwYkbbsyLDc3/Cg3N8YkdzfyLAB0/CgAdMYkQHTyLAmxvCgJsYYkSbGyLBMufCgTLkYkUy5s7h0fyyJdH8EmXR/3Kh0fwSZBKYEmSmZBJlOjLO4BKazuCmZs7hOjCyJBKYsiSmZLIlOjNyoBKbcqCmZ3KhOjASZTLncqEy5s7hMuQSZJsbcqCbGs7gmxgSZAdPcqAHTs7gB0wSZ3N/cqNzfs7jc3wSZtuzcqLbss7i27ASZkfncqJH5s7iR+SyJkfksibbsLInc3yyJAdMsiSbGLIlMuTvoTLlj2Ey5i8hMuTvoJsZj2CbGi8gmxjvoAdNj2AHTi8gB0zvo3N9j2Nzfi8jc3zvotuxj2Lbsi8i27Dvokflj2JH5i8iR+RP4kfkT+LbsE/jc3xP4AdMT+CbGE/hMuUCBbtlAgZTMQIG5v///TLn//0y5QIFMuUCBAdNAgSbG"),
"format": ***********,
"index_count": 1536,
"index_data": PackedByteArray("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"),
"lods": [0.0177494, PackedByteArray("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"), 0.0275848, PackedByteArray("HwFvAAwAHwEMAG4ADQBvAB8BHwFuAAsAewANAB8BfgAfAQsAewAfAX4ACQENAHsACQFqAA0AdgBqAAkBdgAEAGoAdgAuAAQAdwB2AAkBdwAuAHYACgEJAXsAdwAJAQoBCgF7AHoAegB7AH4AaQB3AAoBaQAKAXoAdwCqAC4AaQCqAHcAqgAvAC4AegB+AH0AqgBeAC8ALwBeADAAMACeACMAMAAlAJ4AXgAlADAAXgCfACUAXgAmAJ8AXgCgACYAXgAnAKAAXgCdACcAXgAkAJ0AXgDhACQAXgBWAOEAXgDkAFYAXgBfAOQA5ABfAFUAVQBfAOMAXwBUAOMAXwDiAFQAXwBTAOIAqgDlAF4A5QBdAF4AaQDlAKoAuwBdAOUAuwA3AF0AuwCuADcAQAC7AOUAaQBAAOUAuwDxAK4AuwBAAPEA8QA9AK4AQAC/APEA8QC/AD0AaQC9AEAAQAD1AL8AQAC9APUAvwAHAT0APQAHAbMABwE8ALMABwGyADwAvwD0AAcB9QD0AL8ABwE7ALIA9ADCAAcBwgA7AAcB9QDCAPQAwgDXADsA9QD4AMIA+ADXAMIA+QD4APUAvQD5APUA+AD7ANcA+QD7APgAvQAOAfkAvQBpAA4BaQB6AA4BegB9AA4BDgF9ABABfQB+ABAB+QAOARQB+QAUAfwA+QD8APsADgETARQBDgEQARMBEAF+AJMAEAGTABMBfgALAJMA/AAUAYgAFAETAYgA/ACIAAcAiAATASAABwDMAPwAEwGJACAA/ADMAFAA/ABQAPsAEwEhAIkA+wBQAM0AEwGTACEA+wDNAFEAIQCTACIAIQAiAIoAkwB1ACIA+wBRANcAUQBSANcAUQDOAFIA1wBSALkAdQBrAAgAdQAJAGsAkwBzAHUAcwAJAHUAkwALAHMAcwBsAAkACwBtAHMAcwAKAGwAcwBtAAoAuQA4AK8AuQCvADkAtwC5ADkA1wC5ALcAtwA5ALAA1wC3ADsAtwCwADoAOwC3ALEAtwA6ALEA"), 0.350811, PackedByteArray("fQBvAAwADQBvAH0AfQAMAG4AfQBuAAsAJAENAH0AfQALAIkACwBtAIkAagB9AIkAiQBtAAoAagAuAAQAiQAKAGwAiQBsAAkAiQAJAHUAdQAJAGsAdQBrAAgAiQB1ACIAIQCJACIAIQAiAIoAiACJACAABwCJAIgABwAkAYkABwDMAFAABwBQACQBJAFQAM0AJAHNAFEAUQDOAFIAUQBSALkAuQA4AK8AuQCvADkAUQC5ADkAUQA5ALAAUQCwADoAUQA6ALEAIQFRALEAJAFRACEBJAEhAScBJAEnASIBJAEiASgBIwEkASgBJgEkASMBXQAlASYBJQFdAC4ArgA3AF0ALgBdAF4ALgBeAC8ALwBeADAAMACeACMAMAAlAJ4AXgAlADAAXgCfACUAXgAmAJ8AXgCgACYAXgAnAKAAXgCdACcAXgAkAJ0AXgDhACQAXgBWAOEAXgDkAFYAXgBfAOQA5ABfAFUAVQBfAOMAXwBUAOMAXwDiAFQAXwBTAOIA")],
"material": SubResource("StandardMaterial3D_03lo3"),
"primitive": 3,
"uv_scale": Vector4(1.96714, 1.99073, 0, 0),
"vertex_count": 297,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_g6yur")

[sub_resource type="BoxShape3D" id="BoxShape3D_n1eos"]
size = Vector3(9.92574, 2.18933, 2.01959)

[sub_resource type="BoxShape3D" id="BoxShape3D_qh7xv"]
size = Vector3(8.46973, 1.59479, 0.869278)

[sub_resource type="BoxShape3D" id="BoxShape3D_vbwoa"]
size = Vector3(1.94777, 0.843391, 2.68436)

[sub_resource type="BoxShape3D" id="BoxShape3D_ly2ws"]
size = Vector3(1.20807, 0.843391, 2.15708)

[sub_resource type="BoxShape3D" id="BoxShape3D_eexbc"]
size = Vector3(1.20807, 1.18326, 2.16301)

[sub_resource type="BoxShape3D" id="BoxShape3D_q54y4"]
size = Vector3(1.87178, 1.18326, 3.17819)

[sub_resource type="BoxShape3D" id="BoxShape3D_4rjhi"]
size = Vector3(1.86256, 0.573075, 4.368)

[sub_resource type="BoxShape3D" id="BoxShape3D_iwrab"]
size = Vector3(0.437898, 1.18326, 4.14573)

[sub_resource type="BoxShape3D" id="BoxShape3D_jfdlo"]
size = Vector3(0.237504, 1.18326, 3.2047)

[sub_resource type="BoxShape3D" id="BoxShape3D_q8l68"]
size = Vector3(1.00539, 1.18326, 2.34029)

[sub_resource type="BoxShape3D" id="BoxShape3D_jw0hh"]
size = Vector3(1.00539, 1.41915, 3.21325)

[sub_resource type="BoxShape3D" id="BoxShape3D_x1g7i"]
size = Vector3(2.10878, 0.845524, 2.32785)

[sub_resource type="BoxShape3D" id="BoxShape3D_u8du3"]
size = Vector3(11.6794, 2.18933, 0.481461)

[sub_resource type="BoxShape3D" id="BoxShape3D_2agkh"]
size = Vector3(5.53369, 2.18933, 0.481461)

[sub_resource type="BoxShape3D" id="BoxShape3D_l0wde"]
size = Vector3(2.74463, 1.96869, 0.112801)

[sub_resource type="BoxShape3D" id="BoxShape3D_ehj8d"]
size = Vector3(1.99597, 1.8696, 0.196657)

[sub_resource type="BoxShape3D" id="BoxShape3D_8qk60"]
size = Vector3(2.18796, 2.10655, 0.0983925)

[sub_resource type="BoxShape3D" id="BoxShape3D_123rc"]
size = Vector3(3.31836, 2.51635, 0.0983925)

[sub_resource type="BoxShape3D" id="BoxShape3D_clesg"]
size = Vector3(4.98535, 2.46789, 0.0983925)

[sub_resource type="BoxShape3D" id="BoxShape3D_7qfaj"]
size = Vector3(5.60632, 2.42736, 0.0983925)

[sub_resource type="BoxShape3D" id="BoxShape3D_tpr76"]
size = Vector3(7.61707, 2.67101, 0.0983925)

[sub_resource type="BoxShape3D" id="BoxShape3D_txoqd"]
size = Vector3(15.2831, 2.58166, 1.09607)

[sub_resource type="BoxShape3D" id="BoxShape3D_75flo"]
size = Vector3(16.1064, 2.62902, 1.55011)

[sub_resource type="BoxShape3D" id="BoxShape3D_1q8q6"]
size = Vector3(33.7217, 2.62902, 1.55011)

[sub_resource type="BoxShape3D" id="BoxShape3D_wvvbj"]
size = Vector3(6.0824, 2.18933, 0.436771)

[sub_resource type="BoxShape3D" id="BoxShape3D_olybj"]
size = Vector3(5.35767, 2.18933, 0.436771)

[sub_resource type="BoxShape3D" id="BoxShape3D_xu012"]
size = Vector3(6.12402, 2.18933, 0.436771)

[sub_resource type="BoxShape3D" id="BoxShape3D_23vet"]
size = Vector3(4.67908, 2.18933, 0.436771)

[sub_resource type="BoxShape3D" id="BoxShape3D_k4eby"]
size = Vector3(7.68414, 2.07385, 0.436771)

[sub_resource type="BoxShape3D" id="BoxShape3D_jfgfn"]
size = Vector3(4.10168, 3.38255, 1.05225)

[sub_resource type="BoxShape3D" id="BoxShape3D_looav"]
size = Vector3(10.0071, 4.16644, 0.0644608)

[sub_resource type="BoxShape3D" id="BoxShape3D_b0t1j"]
size = Vector3(2.27112, 1.85156, 0.0644608)

[sub_resource type="BoxShape3D" id="BoxShape3D_5s3lq"]
size = Vector3(1.74161, 1.24365, 0.337893)

[sub_resource type="BoxShape3D" id="BoxShape3D_qw44k"]
size = Vector3(1.31223, 2.45462, 53.346)

[sub_resource type="CylinderShape3D" id="CylinderShape3D_6p20i"]
radius = 1.0

[sub_resource type="CylinderShape3D" id="CylinderShape3D_olp12"]
height = 1.2069
radius = 1.0

[sub_resource type="CylinderShape3D" id="CylinderShape3D_jknre"]
height = 1.2069
radius = 0.676508

[sub_resource type="CylinderShape3D" id="CylinderShape3D_atdhh"]
height = 0.966827
radius = 0.83363

[sub_resource type="CylinderShape3D" id="CylinderShape3D_5aaxs"]
height = 0.503001
radius = 1.40555

[sub_resource type="CylinderShape3D" id="CylinderShape3D_mcwbp"]
radius = 1.8241

[sub_resource type="CylinderShape3D" id="CylinderShape3D_uy6kp"]
height = 4.01145
radius = 2.899

[sub_resource type="CylinderShape3D" id="CylinderShape3D_6jwyo"]
radius = 0.4

[sub_resource type="CylinderShape3D" id="CylinderShape3D_siuev"]
radius = 1.038

[sub_resource type="CylinderShape3D" id="CylinderShape3D_8ks7p"]
radius = 1.038

[sub_resource type="CylinderShape3D" id="CylinderShape3D_hyg4w"]
radius = 1.576

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_qjith"]
bg_color = Color(0.6, 0.6, 0.6, 0.713)

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_74gn8"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_8hidn"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_wan5x"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_ao4ib"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_sn4s7"]
render_priority = 0
shader = ExtResource("110_sn4s7")
shader_parameter/curve_amount = 0.0
shader_parameter/cloud_density = 0.712
shader_parameter/cloud_speed = Vector2(0.05, 0)
shader_parameter/scale_x = 20.0
shader_parameter/scale_y = 25.0
shader_parameter/softness = 0.208

[sub_resource type="SphereMesh" id="SphereMesh_qkeux"]
flip_faces = true
radius = 7.126
height = 4.851
is_hemisphere = true

[node name="WaitingRoom" type="Node3D"]
script = ExtResource("1_xsplj")

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_qkeux")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.866026, -0.433013, -0.25, -4.37114e-08, -0.5, 0.866025, -0.5, -0.75, -0.433012, 0, -2.12889, 0)
light_energy = 1.934

[node name="PlayerSpawner" type="MultiplayerSpawner" parent="." node_paths=PackedStringArray("spawn_points")]
unique_name_in_owner = true
_spawnable_scenes = PackedStringArray("uid://cm26nto6fstcx")
spawn_path = NodePath("../Players")
script = ExtResource("2_wan5x")
player_scene = ExtResource("108_8hidn")
spawn_points = NodePath("../SpawnPoints")

[node name="Players" type="Node3D" parent="."]
unique_name_in_owner = true

[node name="SpawnPoints" type="Node3D" parent="."]
script = ExtResource("110_ao4ib")

[node name="platform" type="CSGBox3D" parent="SpawnPoints"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -12.9125, -0.25271, -31.3231)
visible = false
use_collision = true
size = Vector3(4.20923, 1, 4.89941)

[node name="platform2" type="CSGBox3D" parent="SpawnPoints"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -11.1181, -0.25271, -3.71304)
visible = false
use_collision = true
size = Vector3(4.20923, 1, 4.89941)

[node name="platform3" type="CSGBox3D" parent="SpawnPoints"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 10.0188, -0.25271, 4.95258)
visible = false
use_collision = true
size = Vector3(4.20923, 1, 4.89941)

[node name="platform4" type="CSGBox3D" parent="SpawnPoints"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 8.0276, -0.25271, -19.8861)
visible = false
use_collision = true
size = Vector3(4.20923, 1, 4.89941)

[node name="Environment" type="Node3D" parent="."]
transform = Transform3D(1.5, 0, 0, 0, 1.5, 0, 0, 0, 1.5, 0, 0, 0)

[node name="crops_bambooStageA2" parent="Environment" instance=ExtResource("3_gyvjn")]
visible = false

[node name="crops_bambooStageB2" parent="Environment" instance=ExtResource("4_3eurq")]
visible = false

[node name="crops_cornStageA2" parent="Environment" instance=ExtResource("5_dpnkx")]
visible = false

[node name="crops_leafsStageA2" parent="Environment" instance=ExtResource("6_60ey1")]
visible = false

[node name="crops_leafsStageB2" parent="Environment" instance=ExtResource("7_kocix")]
visible = false

[node name="crops_wheatStageA2" parent="Environment" instance=ExtResource("8_g2t6w")]
visible = false

[node name="crops_wheatStageB2" parent="Environment" instance=ExtResource("9_weblv")]
visible = false

[node name="Grass" type="Node3D" parent="Environment"]

[node name="grass2" parent="Environment/Grass" instance=ExtResource("10_nmen0")]
visible = false

[node name="grass_large2" parent="Environment/Grass" instance=ExtResource("11_xdkde")]
visible = false

[node name="grass_leafs2" parent="Environment/Grass" instance=ExtResource("12_s41v5")]
visible = false

[node name="grass_leafsLarge2" parent="Environment/Grass" instance=ExtResource("13_xm6xh")]
visible = false

[node name="ground_pathRocks2" parent="Environment/Grass" instance=ExtResource("14_0mkfr")]
transform = Transform3D(-2.18557e-07, 0, -5, 0, 5, 0, 5, 0, -2.18557e-07, -8.43368, 0.225116, -7.36422)

[node name="ground_pathRocks10" parent="Environment/Grass" instance=ExtResource("14_0mkfr")]
transform = Transform3D(-2.18557e-07, 0, -5, 0, 5, 0, 5, 0, -2.18557e-07, -3.43368, 0.225116, -7.36422)

[node name="ground_pathRocks11" parent="Environment/Grass" instance=ExtResource("14_0mkfr")]
transform = Transform3D(-2.18557e-07, 0, -5, 0, 5, 0, 5, 0, -2.18557e-07, -3.43368, 0.225116, -7.36422)

[node name="ground_pathRocks12" parent="Environment/Grass" instance=ExtResource("14_0mkfr")]
transform = Transform3D(-2.18557e-07, 0, -5, 0, 5, 0, 5, 0, -2.18557e-07, 1.56632, 0.225116, -7.36422)

[node name="ground_pathRocks13" parent="Environment/Grass" instance=ExtResource("14_0mkfr")]
transform = Transform3D(-2.18557e-07, 0, -5, 0, 5, 0, 5, 0, -2.18557e-07, 6.56632, 0.225116, -7.36422)

[node name="ground_pathRocks14" parent="Environment/Grass" instance=ExtResource("14_0mkfr")]
transform = Transform3D(-2.18557e-07, 0, -5, 0, 5, 0, 5, 0, -2.18557e-07, 11.4757, 0.225116, -7.36422)

[node name="grass3" parent="Environment/Grass" instance=ExtResource("10_nmen0")]
visible = false

[node name="grass_large3" parent="Environment/Grass" instance=ExtResource("11_xdkde")]
visible = false

[node name="grass_leafs3" parent="Environment/Grass" instance=ExtResource("12_s41v5")]
visible = false

[node name="grass_leafsLarge3" parent="Environment/Grass" instance=ExtResource("13_xm6xh")]
visible = false

[node name="Ground" type="Node3D" parent="Environment/Grass"]

[node name="ground_grass2" parent="Environment/Grass/Ground" instance=ExtResource("15_xqrf6")]
transform = Transform3D(10, 0, 0, 0, 10, 0, 0, 0, 10, 0, 0.484709, 0)

[node name="ground_grass4" parent="Environment/Grass/Ground" instance=ExtResource("15_xqrf6")]
transform = Transform3D(9.13663, 0, 0, 0, 10, 0, 0, 0, 10, 9.41803, 0.484709, 0)

[node name="ground_grass5" parent="Environment/Grass/Ground" instance=ExtResource("15_xqrf6")]
transform = Transform3D(6.40015, 0, 0, 0, 9.94099, 0, 0, 0, 9.94099, -7.71459, 0.484709, 0.036974)

[node name="ground_grass8" parent="Environment/Grass/Ground" instance=ExtResource("15_xqrf6")]
transform = Transform3D(39.717, 0, 0, 0, 9.98331, 0, 0, 0, 9.98331, -5.78847, 0.484709, 9.9184)

[node name="ground_grass9" parent="Environment/Grass/Ground" instance=ExtResource("15_xqrf6")]
transform = Transform3D(39.717, 0, 0, 0, 9.98331, 0, 0, 0, 9.98331, -5.87771, 0.484709, -24.0816)

[node name="ground_grass3" parent="Environment/Grass/Ground" instance=ExtResource("15_xqrf6")]
transform = Transform3D(-10, 0, 9.53518e-07, 0, 10, 0, -8.74228e-07, 0, -10.907, -0.0328087, 0.484709, -15.2365)

[node name="ground_grass6" parent="Environment/Grass/Ground" instance=ExtResource("15_xqrf6")]
transform = Transform3D(-6.00468, 0, 9.38687e-07, 0, 10, 0, -5.24946e-07, 0, -10.7373, -7.93221, 0.484709, -15.1759)

[node name="ground_grass7" parent="Environment/Grass/Ground" instance=ExtResource("15_xqrf6")]
transform = Transform3D(-9.17024, 0, 9.47891e-07, 0, 9.94099, 0, -8.01688e-07, 0, -10.8426, 9.38414, 0.484709, -15.2365)

[node name="cliff_block_rock2" parent="Environment/Grass/Ground" instance=ExtResource("16_eh8g7")]
transform = Transform3D(7, 0, 0, 0, 5, 0, 0, 0, 7, -26.939, -1, 4.51784)

[node name="cliff_block_rock4" parent="Environment/Grass/Ground" instance=ExtResource("16_eh8g7")]
transform = Transform3D(7, 0, 0, 0, 5, 0, 0, 0, 7, -26.939, -1, 11.1845)

[node name="cliff_top_rock3" parent="Environment/Grass/Ground" instance=ExtResource("17_e0407")]
transform = Transform3D(5.05483, 0, 0, 0, 4.99445, 0, 0, 0, 7, -20.9211, 0, 11.1732)
visible = false

[node name="grass_leafsLarge4" parent="Environment/Grass" instance=ExtResource("13_xm6xh")]
transform = Transform3D(1.61941, 0, 1.17368, 0, 2, 0, -1.17368, 0, 1.61941, -17.5306, 2.28551, 13.3685)

[node name="grass_leafsLarge5" parent="Environment/Grass" instance=ExtResource("13_xm6xh")]
transform = Transform3D(0.94707, 0, 1.76155, 0, 2, 0, -1.76155, 0, 0.94707, -16.7998, 2.28551, 13.963)

[node name="grass_leafsLarge6" parent="Environment/Grass" instance=ExtResource("13_xm6xh")]
transform = Transform3D(0.94707, 0, 1.76155, 0, 2, 0, -1.76155, 0, 0.94707, -16.3263, 2.28551, 13.0822)

[node name="grass_leafsLarge7" parent="Environment/Grass" instance=ExtResource("13_xm6xh")]
transform = Transform3D(0.94707, 0, 1.76155, 0, 2, 0, -1.76155, 0, 0.94707, -15.9088, 2.28551, 14.3483)

[node name="grass_leafsLarge8" parent="Environment/Grass" instance=ExtResource("13_xm6xh")]
transform = Transform3D(0.281872, 0, 1.98004, 0, 2, 0, -1.98004, 0, 0.281872, -15.4353, 2.28551, 13.4675)

[node name="grass_leafsLarge9" parent="Environment/Grass" instance=ExtResource("13_xm6xh")]
transform = Transform3D(-0.0605188, 0, 1.99908, 0, 2, 0, -1.99908, 0, -0.0605188, -14.9925, 2.28551, 14.754)

[node name="grass_leafsLarge10" parent="Environment/Grass" instance=ExtResource("13_xm6xh")]
transform = Transform3D(1.74787, 0, 0.972091, 0, 2, 0, -0.972091, 0, 1.74787, -14.519, 2.28551, 13.8732)

[node name="grass_leafsLarge11" parent="Environment/Grass" instance=ExtResource("13_xm6xh")]
transform = Transform3D(1.3109, 0, 0.729068, 0, 1.5, 0, -0.729068, 0, 1.3109, -13.7966, 2.28551, 14.1632)

[node name="platform_grass2" parent="Environment/Grass" instance=ExtResource("18_14tf0")]
transform = Transform3D(-0.0318867, 0, -2.99983, 0, 4, 0, 2.99983, 0, -0.0318867, 13.2356, 0.25304, 8.72683)

[node name="Water" type="Node3D" parent="Environment"]
transform = Transform3D(0.558735, 0, 0, 0, 1, 0, 0, 0, 0.260484, 0, -0.141734, -6.576)

[node name="MeshInstance3D2" type="MeshInstance3D" parent="Environment/Water"]
transform = Transform3D(6.98003, 0, 0, 0, 1.01007, 0, 0, 0, 8.95118, -9.90368, -0.081, -2.52167)
mesh = SubResource("PlaneMesh_28ggn")

[node name="MeshInstance3D3" type="MeshInstance3D" parent="Environment/Water"]
transform = Transform3D(4.61295, 0, 0, 0, 1.01007, 0, 0, 0, 6.35771, 2.11898, -0.081, -11.224)
visible = false
mesh = SubResource("PlaneMesh_28ggn")

[node name="PathStone" type="Node3D" parent="Environment"]

[node name="path_stone2" parent="Environment/PathStone" instance=ExtResource("19_puwsv")]
transform = Transform3D(-1.31134e-07, 0, -3, 0, 3, 0, 3, 0, -1.31134e-07, -2.34565, 0.0961289, 1.12314)

[node name="path_stone3" parent="Environment/PathStone" instance=ExtResource("19_puwsv")]
transform = Transform3D(-1.31134e-07, 0, -3, 0, 3, 0, 3, 0, -1.31134e-07, -0.345654, 0.0961289, 1.12314)

[node name="path_stone4" parent="Environment/PathStone" instance=ExtResource("19_puwsv")]
transform = Transform3D(-1.31134e-07, 0, -3, 0, 3, 0, 3, 0, -1.31134e-07, -2.34565, 0.0961289, 4.12314)

[node name="path_stone5" parent="Environment/PathStone" instance=ExtResource("19_puwsv")]
transform = Transform3D(-1.31134e-07, 0, -3, 0, 3, 0, 3, 0, -1.31134e-07, -0.345654, 0.0961289, 4.12314)

[node name="path_stone6" parent="Environment/PathStone" instance=ExtResource("19_puwsv")]
transform = Transform3D(3.93402e-07, 0, 3, 0, 3, 0, -3, 0, 3.93402e-07, -0.327864, 0.0961289, -14.4514)

[node name="path_stone7" parent="Environment/PathStone" instance=ExtResource("19_puwsv")]
transform = Transform3D(3.93402e-07, 0, 3, 0, 3, 0, -3, 0, 3.93402e-07, -2.32787, 0.0961289, -14.4514)

[node name="path_stone8" parent="Environment/PathStone" instance=ExtResource("19_puwsv")]
transform = Transform3D(3.93402e-07, 0, 3, 0, 3, 0, -3, 0, 3.93402e-07, 1.92414, 0.0961289, -18.1047)
visible = false

[node name="path_stone9" parent="Environment/PathStone" instance=ExtResource("19_puwsv")]
transform = Transform3D(3.93402e-07, 0, 3, 0, 3, 0, -3, 0, 3.93402e-07, -0.075861, 0.0961289, -18.1047)
visible = false

[node name="path_stoneCircle2" parent="Environment/PathStone" instance=ExtResource("20_0t4ge")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1.41389, 0)
visible = false

[node name="path_stoneCorner2" parent="Environment/PathStone" instance=ExtResource("21_e5fwn")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.196942, 0)
visible = false

[node name="path_stoneEnd2" parent="Environment/PathStone" instance=ExtResource("22_c8mww")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.217927, 0)
visible = false

[node name="statue_ring2" parent="Environment/PathStone" instance=ExtResource("23_1b17j")]
transform = Transform3D(2.82843, 0, 2.82843, 0, 4, 0, -2.82843, 0, 2.82843, 9.90832, 0.0943146, 8.6886)

[node name="path_stoneCircle3" parent="Environment/PathStone" instance=ExtResource("20_0t4ge")]
transform = Transform3D(2.89778, 0, -0.776457, 0, 2, 0, 0.776457, 0, 2.89778, 8.28524, 0.0589164, 7.09772)

[node name="bridge_woodRoundNarrow2" parent="Environment" instance=ExtResource("24_enwkh")]
transform = Transform3D(-3.49691e-07, 0, 7.5, 0, 7.5, 0, -8, 0, -3.27835e-07, 0, 0, -7.50876)
visible = false

[node name="bridge_side_woodRound2" parent="Environment" instance=ExtResource("25_gxvs6")]
transform = Transform3D(-1.74846e-07, 0, 4, 0, 4, 0, -4, 0, -1.74846e-07, -1.32035, 0.182264, -2.85525)

[node name="bridge_side_woodRound3" parent="Environment" instance=ExtResource("25_gxvs6")]
transform = Transform3D(-1.74846e-07, 0, -4, 0, 4, 0, 4, 0, -1.74846e-07, -1.32035, 0.179318, -10.7374)

[node name="bridge_center_woodRound2" parent="Environment" instance=ExtResource("26_y6o7m")]
transform = Transform3D(-1.74846e-07, 0, 4, 0, 4, 0, -4, 0, -1.74846e-07, -1.32035, 0.179329, -6.84659)

[node name="ground_riverSideOpen2" parent="Environment" instance=ExtResource("27_0bqvo")]
transform = Transform3D(-2.18557e-07, 0, 5, 0, 5, 0, -5, 0, -2.18557e-07, -13.3654, 0.225, -7.37431)

[node name="ground_riverSide2" parent="Environment" instance=ExtResource("28_hauqx")]
transform = Transform3D(-2.18557e-07, 0, -5, 0, 5, 0, 5, 0, -2.18557e-07, -13.362, 0.225, -3)

[node name="ground_riverSide3" parent="Environment" instance=ExtResource("28_hauqx")]
transform = Transform3D(-2.18557e-07, 0, -5, 0, 5, 0, 5, 0, -2.18557e-07, -13.362, 0.225, -12)

[node name="ground_riverCorner2" parent="Environment" instance=ExtResource("29_3jeqs")]
transform = Transform3D(-4.99971, 0, 5.18959e-07, 0, 4.99971, 0, -4.37089e-07, 0, -5.9362, -13.3526, 0.225, 2.04595)

[node name="ground_riverCorner3" parent="Environment" instance=ExtResource("29_3jeqs")]
transform = Transform3D(-2.18557e-07, 0, -5, 0, 5, 0, 5, 0, -2.18557e-07, -13.3526, 0.225, -17.0049)

[node name="ground_riverSide5" parent="Environment" instance=ExtResource("28_hauqx")]
transform = Transform3D(-5.00855, 0, -8.96311e-07, 0, 5.258, 0, 7.5627e-07, 0, -5.936, -18.3226, 0.225, 2.04348)

[node name="ground_riverSide6" parent="Environment" instance=ExtResource("28_hauqx")]
transform = Transform3D(5, 0, 3.17865e-07, 0, 5, 0, -3.17865e-07, 0, 5, -18.3519, 0.225, -17.0022)

[node name="ground_riverOpen2" parent="Environment" instance=ExtResource("30_awkkw")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -18.3339, 0.225, -3.34595)

[node name="ground_riverOpen3" parent="Environment" instance=ExtResource("30_awkkw")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -18.3339, 0.225, -8.34595)

[node name="ground_riverOpen4" parent="Environment" instance=ExtResource("30_awkkw")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -18.3339, 0.225, -13.346)

[node name="ground_riverOpen5" parent="Environment" instance=ExtResource("30_awkkw")]
transform = Transform3D(4.87883, 0, 0, 0, 4.87883, 0, 0, 0, 3.36232, -18.3912, 0.225, -17.8277)

[node name="ground_riverSide7" parent="Environment" instance=ExtResource("28_hauqx")]
transform = Transform3D(-5.00855, 0, -8.96311e-07, 0, 5.258, 0, 7.5627e-07, 0, -5.936, -23.0846, 0.225, 2.01845)

[node name="ground_riverSide8" parent="Environment" instance=ExtResource("28_hauqx")]
transform = Transform3D(5, 0, 3.17865e-07, 0, 5, 0, -3.17865e-07, 0, 5, -23.0846, 0.225, -17.0022)

[node name="ground_riverOpen6" parent="Environment" instance=ExtResource("30_awkkw")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -23.0666, 0.225, -3.34595)

[node name="ground_riverOpen7" parent="Environment" instance=ExtResource("30_awkkw")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -23.0666, 0.225, -8.34595)

[node name="ground_riverOpen8" parent="Environment" instance=ExtResource("30_awkkw")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -23.0666, 0.225, -13.346)

[node name="ground_riverOpen9" parent="Environment" instance=ExtResource("30_awkkw")]
transform = Transform3D(4.87883, 0, 0, 0, 4.87883, 0, 0, 0, 3.36232, -23.0093, 0.225, -17.8277)

[node name="Cliff" type="Node3D" parent="Environment"]

[node name="cliff_blockHalf_stone2" parent="Environment/Cliff" instance=ExtResource("31_ckhmy")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -27, 0, 13)
visible = false

[node name="cliff_waterfallTop_stone2" parent="Environment/Cliff" instance=ExtResource("32_c2m0n")]
transform = Transform3D(5.28485e-07, 0, -7, 0, 10.2752, 0, 7, 0, 5.28485e-07, -22.0919, 0.077868, -7.38097)
visible = false

[node name="cliff_top_stone2" parent="Environment/Cliff" instance=ExtResource("33_cre7x")]
transform = Transform3D(-3.0598e-07, 0, -7, 0, 10.2752, 0, 7, 0, -3.0598e-07, -22.0919, 0.077868, -0.380971)
visible = false

[node name="cliff_top_stone4" parent="Environment/Cliff" instance=ExtResource("33_cre7x")]
transform = Transform3D(-3.0598e-07, 0, -7, 0, 10.2752, 0, 7, 0, -3.0598e-07, -22.0919, 0.077868, -14.381)
visible = false

[node name="cliff_top_rock2" parent="Environment/Cliff" instance=ExtResource("17_e0407")]
transform = Transform3D(-3.0598e-07, 0, -7, 0, 10.2752, 0, 7, 0, -3.0598e-07, -22.0919, 0.077868, -0.380971)

[node name="cliff_top_rock4" parent="Environment/Cliff" instance=ExtResource("17_e0407")]
transform = Transform3D(-3.0598e-07, 0, -7, 0, 10.2752, 0, 7, 0, -3.0598e-07, -27.8844, 0.077868, 12.4853)

[node name="cliff_top_rock3" parent="Environment/Cliff" instance=ExtResource("17_e0407")]
transform = Transform3D(-3.0598e-07, 0, -7, 0, 10.2752, 0, 7, 0, -3.0598e-07, -22.0919, 0.077868, -14.381)

[node name="cliff_top_rock6" parent="Environment/Cliff" instance=ExtResource("17_e0407")]
transform = Transform3D(-2.62268e-07, 0, -6, 0, 10.275, 0, 6, 0, -2.62268e-07, 16.9215, -9.79165, 1.02448)

[node name="cliff_top_rock8" parent="Environment/Cliff" instance=ExtResource("17_e0407")]
transform = Transform3D(-2.62268e-07, 0, -6, 0, 10.275, 0, 6, 0, -2.62268e-07, 16.9215, -9.79165, -2.96518)

[node name="cliff_top_rock9" parent="Environment/Cliff" instance=ExtResource("17_e0407")]
transform = Transform3D(-2.62268e-07, 0, -6, 0, 10.275, 0, 6, 0, -2.62268e-07, 16.9215, -9.79165, -11.7809)

[node name="cliff_top_rock10" parent="Environment/Cliff" instance=ExtResource("17_e0407")]
transform = Transform3D(-2.62268e-07, 0, -6, 0, 10.275, 0, 6, 0, -2.62268e-07, 16.9215, -9.79165, -17.7809)

[node name="cliff_top_rock11" parent="Environment/Cliff" instance=ExtResource("17_e0407")]
transform = Transform3D(-2.62268e-07, 0, -6, 0, 10.275, 0, 6, 0, -2.62268e-07, 16.9215, -9.79165, -23.7809)

[node name="cliff_top_rock7" parent="Environment/Cliff" instance=ExtResource("17_e0407")]
transform = Transform3D(-2.62268e-07, 0, -6, 0, 10.275, 0, 6, 0, -2.62268e-07, 16.9215, -9.79165, 7.02448)

[node name="cliff_top_rock5" parent="Environment/Cliff" instance=ExtResource("17_e0407")]
transform = Transform3D(-4.4828, 0, -6.62605, 0, 10.666, 0, 6.62605, 0, -4.4828, -23.5642, -1.88144, -23.3108)

[node name="cliff_waterfallTop_rock2" parent="Environment/Cliff" instance=ExtResource("34_ojfpl")]
transform = Transform3D(-3.0598e-07, 0, -7, 0, 10.275, 0, 7, 0, -3.0598e-07, -22.0919, 0.077868, -7.38097)

[node name="cliff_topDiagonal_rock2" parent="Environment/Cliff" instance=ExtResource("35_k1niv")]
transform = Transform3D(-1.14044e-06, 0, -7, 0, 10.275, 0, 7, 0, -1.14044e-06, -29.0918, 0.0882108, 6.59694)

[node name="cliff_topDiagonal_rock3" parent="Environment/Cliff" instance=ExtResource("35_k1niv")]
transform = Transform3D(7, 0, -8.34465e-07, 0, 10.275, 0, 8.34465e-07, 0, 7, -29.0918, 0.0882108, -21.3268)

[node name="cliff_stepsCornerInner_rock2" type="Node3D" parent="Environment/Cliff"]
transform = Transform3D(5, 0, 0, 0, 3.95912, 0, 0, 0, 5, -20.9733, 0, 12.1676)

[node name="tmpParent" type="Node3D" parent="Environment/Cliff/cliff_stepsCornerInner_rock2"]

[node name="cliff_stepsCornerInner_rock" type="MeshInstance3D" parent="Environment/Cliff/cliff_stepsCornerInner_rock2/tmpParent"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.05, 0)
mesh = SubResource("ArrayMesh_ou0lw")
skeleton = NodePath("")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Environment/Cliff/cliff_stepsCornerInner_rock2/tmpParent"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.390464, 0)
shape = SubResource("ConcavePolygonShape3D_2r06b")

[node name="cliff_steps_rock2" type="Node3D" parent="Environment/Cliff"]
transform = Transform3D(-2.18557e-07, 0, -5, 0, 3.8, 0, 5, 0, -2.18557e-07, -20.9733, 0.150187, 7.19104)

[node name="tmpParent" type="StaticBody3D" parent="Environment/Cliff/cliff_steps_rock2"]

[node name="cliff_steps_rock" type="MeshInstance3D" parent="Environment/Cliff/cliff_steps_rock2/tmpParent"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.05, 0)
mesh = SubResource("ArrayMesh_52ixr")
skeleton = NodePath("")

[node name="CollisionShape3D" type="CollisionShape3D" parent="Environment/Cliff/cliff_steps_rock2/tmpParent"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.05, 0)
shape = SubResource("ConcavePolygonShape3D_af17c")

[node name="Trees" type="Node3D" parent="Environment"]

[node name="tree_blocks_dark2" parent="Environment/Trees" instance=ExtResource("36_ifv8m")]
transform = Transform3D(6.76148, 0, -1.81173, 0, 7, 0, 1.81173, 0, 6.76148, -19.4302, 0, -24)

[node name="tree_blocks_dark4" parent="Environment/Trees" instance=ExtResource("36_ifv8m")]
transform = Transform3D(6.9282, 0, 4, 0, 8, 0, -4, 0, 6.9282, -12.3036, 0, -25.231)

[node name="tree_cone_dark2" parent="Environment/Trees" instance=ExtResource("37_272hi")]
transform = Transform3D(7, 0, 0, 0, 7, 0, 0, 0, 7, -12, 0, -32)
visible = false

[node name="tree_default_dark2" parent="Environment/Trees" instance=ExtResource("38_cldpd")]
transform = Transform3D(6, 0, 0, 0, 6, 0, 0, 0, 6, -11.0124, 0, -40.43)
visible = false

[node name="tree_detailed_dark2" parent="Environment/Trees" instance=ExtResource("39_nkln6")]
transform = Transform3D(4.82963, 0, 1.29409, 0, 6, 0, -1.29409, 0, 4.82963, -27.5836, 4.0659, 10.9429)

[node name="tree_fat_darkh2" parent="Environment/Trees" instance=ExtResource("40_447mi")]
transform = Transform3D(7, 0, 0, 0, 7, 0, 0, 0, 7, 6, 0, -32)
visible = false

[node name="tree_oak_dark2" parent="Environment/Trees" instance=ExtResource("41_8b7cp")]
transform = Transform3D(7, 0, 0, 0, 7, 0, 0, 0, 7, 12, 0, -36)
visible = false

[node name="tree_pineDefaultB2" parent="Environment/Trees" instance=ExtResource("42_54af6")]
transform = Transform3D(4.31504, 0, 2.52595, 0, 5, 0, -2.52595, 0, 4.31504, -12.3149, 0, 4.0171)

[node name="tree_pineDefaultB3" parent="Environment/Trees" instance=ExtResource("42_54af6")]
transform = Transform3D(3.45203, 0, 2.02076, 0, 4, 0, -2.02076, 0, 3.45203, -11.3208, 0, 2.04892)

[node name="tree_pineRoundD2" parent="Environment/Trees" instance=ExtResource("43_b5052")]
transform = Transform3D(6, 0, 0, 0, 6, 0, 0, 0, 6, -15.6158, 0.188593, -23.0993)

[node name="tree_pineRoundD4" parent="Environment/Trees" instance=ExtResource("43_b5052")]
transform = Transform3D(-3.60625, 0, -3.66961, 0, 5.145, 0, 3.66961, 0, -3.60625, 11.1756, 0.188593, 2.98846)

[node name="tree_pineRoundD3" parent="Environment/Trees" instance=ExtResource("43_b5052")]
transform = Transform3D(3.04747, 0, 2.59094, 0, 4, 0, -2.59094, 0, 3.04747, -14.0422, 2.49567, 16.6134)

[node name="tree_pineRoundE3" parent="Environment/Trees" instance=ExtResource("44_jep2d")]
transform = Transform3D(3.23988, 0, -2.34589, 0, 4, 0, 2.34589, 0, 3.23988, -10.2753, 0.210422, 3.81643)

[node name="tree_pineDefaultB4" parent="Environment/Trees" instance=ExtResource("42_54af6")]
transform = Transform3D(-4.44018, 0, -2.5992, 0, 5.145, 0, 2.5992, 0, -4.44018, 12.7009, 0, 3.8545)

[node name="tree_pineRoundE4" parent="Environment/Trees" instance=ExtResource("44_jep2d")]
transform = Transform3D(-4.97645, 0, -1.30611, 0, 5.145, 0, 1.30611, 0, -4.97645, 10.7139, 0.210422, 6.11646)

[node name="tree_pineTallC_detailed2" parent="Environment/Trees" instance=ExtResource("45_wadkt")]
visible = false

[node name="tree_pineGroundB2" parent="Environment/Trees" instance=ExtResource("46_jbech")]
transform = Transform3D(6.90169, 0, -1.16907, 0, 7, 0, 1.16907, 0, 6.90169, -24.9965, 0, -20.6265)

[node name="tree_pineGroundB3" parent="Environment/Trees" instance=ExtResource("46_jbech")]
transform = Transform3D(6.01832, 0, -6.69177, 0, 9, 0, 6.69177, 0, 6.01832, -23.7343, 0, -24.1287)

[node name="tree_pineRoundC2" parent="Environment/Trees" instance=ExtResource("47_h82sl")]
transform = Transform3D(4.05409, 0, 3.16787, 0, 5.145, 0, -3.16787, 0, 4.05409, 12.9335, 0, 6.35647)

[node name="tree_pineSmallC2" parent="Environment/Trees" instance=ExtResource("48_8nthy")]
transform = Transform3D(5.145, 0, 0, 0, 5.145, 0, 0, 0, 5.145, 12.65, 0.120429, 2.25062)

[node name="tree_pineTallA_detailed2" parent="Environment/Trees" instance=ExtResource("49_1xtvl")]
transform = Transform3D(5.05888, 0, -0.937424, 0, 5.145, 0, 0.937424, 0, 5.05888, 12.7971, 0, 0.646365)

[node name="tree_pineTallB2" parent="Environment/Trees" instance=ExtResource("50_ntc7p")]
transform = Transform3D(5.145, 0, 0, 0, 5.145, 0, 0, 0, 5.145, 11.4061, 0, 4.9407)

[node name="tree_pineDefaultB5" parent="Environment/Trees" instance=ExtResource("42_54af6")]
transform = Transform3D(2.31158, 0, 4.59648, 0, 5.145, 0, -4.59648, 0, 2.31158, 10.0603, 0, -1.81719)

[node name="tree_pineRoundE6" parent="Environment/Trees" instance=ExtResource("44_jep2d")]
transform = Transform3D(4.46884, 0, 2.54961, 0, 5.145, 0, -2.54961, 0, 4.46884, 11.4244, 0.210422, -4.76445)

[node name="tree_pineRoundE7" parent="Environment/Trees" instance=ExtResource("44_jep2d")]
transform = Transform3D(4.13862, 0, -3.05661, 0, 5.145, 0, 3.05661, 0, 4.13862, 10.9918, 0.210422, -0.186227)

[node name="tree_pineSmallC3" parent="Environment/Trees" instance=ExtResource("48_8nthy")]
transform = Transform3D(-4.96969, 0, -1.33163, 0, 5.145, 0, 1.33163, 0, -4.96969, 12.3411, 0.120429, -1.37912)

[node name="tree_pineTallA_detailed3" parent="Environment/Trees" instance=ExtResource("49_1xtvl")]
transform = Transform3D(-5.12913, 0, -0.403853, 0, 5.145, 0, 0.403853, 0, -5.12913, 10.7977, 0, 1.30653)

[node name="tree_pineTallB3" parent="Environment/Trees" instance=ExtResource("50_ntc7p")]
transform = Transform3D(-4.96969, 0, -1.33163, 0, 5.145, 0, 1.33163, 0, -4.96969, 13.2726, 0, -3.74002)

[node name="tree_pineTallB4" parent="Environment/Trees" instance=ExtResource("50_ntc7p")]
transform = Transform3D(-5.14266, 0, 0.155056, 0, 5.145, 0, -0.155056, 0, -5.14266, 13.4545, 0, -1.48247)

[node name="tree_pineTallB5" parent="Environment/Trees" instance=ExtResource("50_ntc7p")]
transform = Transform3D(-5.06856, 0, 0.883601, 0, 5.145, 0, -0.883601, 0, -5.06856, 10.7436, 0, -3.27356)

[node name="Stone" type="Node3D" parent="Environment"]

[node name="stone_largeF2" parent="Environment/Stone" instance=ExtResource("51_3ik7o")]
transform = Transform3D(-0.725975, 0, 0.687721, 0, 1, 0, -0.687721, 0, -0.725975, -10.6854, 0, -9.39886)

[node name="stone_smallA2" parent="Environment/Stone" instance=ExtResource("52_nll44")]
transform = Transform3D(1.93172, 0, 0.518144, 0, 4, 0, -0.518144, 0, 1.93172, -10.4248, 0, -10.8846)

[node name="stone_smallB2" parent="Environment/Stone" instance=ExtResource("53_3gjjd")]
transform = Transform3D(-2.07379, 0, 1.39621, 0, 4, 0, -1.39621, 0, -2.07379, -11.0443, 0, -10.2908)

[node name="stone_smallC2" parent="Environment/Stone" instance=ExtResource("54_mqwrx")]
transform = Transform3D(1.48843, 0, 0.185974, 0, 1.5, 0, -0.185974, 0, 1.48843, -9.84675, 0.29816, -10.5884)

[node name="stone_smallD2" parent="Environment/Stone" instance=ExtResource("55_3xp12")]
transform = Transform3D(2.32267, 0, -0.924772, 0, 1, 0, 0.924772, 0, 2.32267, -9.82493, 0.0470245, -10.5431)

[node name="stone_smallE2" parent="Environment/Stone" instance=ExtResource("56_7usa1")]
transform = Transform3D(-2.59808, 0, -1.5, 0, 7, 0, 1.5, 0, -2.59808, -10.4253, 0.346584, -10.1545)

[node name="stone_tallA2" parent="Environment/Stone" instance=ExtResource("57_uql5m")]
transform = Transform3D(-1.2941, 0, -4.82963, 0, 7, 0, 4.82963, 0, -1.2941, -23.094, 0, -1.90184)
visible = false

[node name="stone_tallB2" parent="Environment/Stone" instance=ExtResource("58_qpy2l")]
transform = Transform3D(5.833, 0, 0, 0, 7, 0, 0, 0, 5.833, -23.0857, -0.066582, -13.0729)
visible = false

[node name="Rock" type="Node3D" parent="Environment"]

[node name="rock_largeA2" parent="Environment/Rock" instance=ExtResource("59_47uvh")]
transform = Transform3D(1.94979, 0, -0.445344, 0, 2, 0, 0.445344, 0, 1.94979, -9.98525, 0, 0.620174)

[node name="rock_largeB2" parent="Environment/Rock" instance=ExtResource("60_h3iex")]
transform = Transform3D(-2.21011, 0, 1.16852, 0, 2.5, 0, -1.16852, 0, -2.21011, -9.12086, 0, 1.90682)

[node name="rock_smallI2" parent="Environment/Rock" instance=ExtResource("61_fv6dp")]
transform = Transform3D(0.592553, 0, 1.91021, 0, 2, 0, -1.91021, 0, 0.592553, -8.95372, 0, 0.241185)

[node name="rock_largeE2" parent="Environment/Rock" instance=ExtResource("62_fkodr")]
transform = Transform3D(0.481837, 0, 1.4205, 0, 1.5, 0, -1.4205, 0, 0.481837, -14.8923, -0.356874, -11.5843)

[node name="rock_tallA2" parent="Environment/Rock" instance=ExtResource("63_t0hgs")]
transform = Transform3D(-1.2941, 0, -4.82963, 0, 7, 0, 4.82963, 0, -1.2941, -23.094, 0, -1.90184)
visible = false

[node name="rock_tallB2" parent="Environment/Rock" instance=ExtResource("64_jrafx")]
transform = Transform3D(5.833, 0, 0, 0, 7, 0, 0, 0, 5.833, -23.0857, -0.066582, -13.0729)
visible = false

[node name="rock_tallB3" type="Node3D" parent="Environment/Rock"]
transform = Transform3D(20, 0, 0, 0, 20, 0, 0, 0, 20, -0.378697, -0.066582, -33.4205)

[node name="tmpParent" type="Node3D" parent="Environment/Rock/rock_tallB3"]

[node name="rock_tallB" type="MeshInstance3D" parent="Environment/Rock/rock_tallB3/tmpParent"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.0840785, -0.05, 0)
mesh = SubResource("ArrayMesh_615qo")
skeleton = NodePath("")

[node name="stone_tallA2" parent="Environment/Rock" instance=ExtResource("57_uql5m")]
transform = Transform3D(-1.2941, 0, -4.82963, 0, 8, 0, 4.82963, 0, -1.2941, -23.3781, 0, -1.90184)

[node name="stone_tallB2" parent="Environment/Rock" instance=ExtResource("58_qpy2l")]
transform = Transform3D(6, 0, 0, 0, 7.2, 0, 0, 0, 6, -23.0857, -0.066582, -13.0729)

[node name="rock_smallFlatC2" parent="Environment/Rock" instance=ExtResource("65_o24vq")]
transform = Transform3D(-1.82637, 0, 2.38, 0, 3, 0, -2.38, 0, -1.82637, -13.2955, 2.34706, 15.9511)

[node name="rock_smallTopA2" parent="Environment/Rock" instance=ExtResource("66_jyhum")]
transform = Transform3D(-0.0564931, 0, 2.99947, 0, 4, 0, -2.99947, 0, -0.0564931, 12.8683, -0.0220443, -8.07586)

[node name="rock_smallTopB2" parent="Environment/Rock" instance=ExtResource("67_b4c6x")]
transform = Transform3D(-0.482279, 0, -2.96098, 0, 3, 0, 2.96098, 0, -0.482279, 13.1869, -0.143914, -6.9743)

[node name="Plants" type="Node3D" parent="Environment"]

[node name="plant_bush2" parent="Environment/Plants" instance=ExtResource("68_efa5t")]
transform = Transform3D(1.5, 0, 0, 0, 1.5, 0, 0, 0, 1.5, -7.90627, 0, -1.99761)

[node name="plant_bush4" parent="Environment/Plants" instance=ExtResource("68_efa5t")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -24.8865, 4.14341, 12.8727)

[node name="plant_bush5" parent="Environment/Plants" instance=ExtResource("68_efa5t")]
transform = Transform3D(1.3415, 0, -1.48337, 0, 2, 0, 1.48337, 0, 1.3415, -24.8865, 4.14341, 12.2987)

[node name="plant_bush6" parent="Environment/Plants" instance=ExtResource("68_efa5t")]
transform = Transform3D(1.3415, 0, -1.48337, 0, 2, 0, 1.48337, 0, 1.3415, -25.5532, 4.14341, 12.2987)

[node name="plant_bush7" parent="Environment/Plants" instance=ExtResource("68_efa5t")]
transform = Transform3D(0.134533, 0, -1.99547, 0, 2, 0, 1.99547, 0, 0.134533, -24.387, 4.14341, 11.5533)

[node name="plant_bush3" parent="Environment/Plants" instance=ExtResource("68_efa5t")]
transform = Transform3D(1.34272, 0, 0.668664, 0, 1.5, 0, -0.668664, 0, 1.34272, -9.2396, 0, -9.99761)

[node name="plant_bushLarge2" parent="Environment/Plants" instance=ExtResource("69_qqd5g")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -9.68429, 0, -9.73795)

[node name="plant_bushLargeTriangle2" parent="Environment/Plants" instance=ExtResource("70_pxdjd")]
transform = Transform3D(1.96047, 0, -0.395691, 0, 2, 0, 0.395691, 0, 1.96047, -14.1936, 2.28551, 15.2387)

[node name="plant_bushLargeTriangle3" parent="Environment/Plants" instance=ExtResource("70_pxdjd")]
transform = Transform3D(1.96427, 0, 0.37634, 0, 2, 0, -0.37634, 0, 1.96427, -14.3223, 2.28551, 15.6282)

[node name="plant_bushLargeTriangle4" parent="Environment/Plants" instance=ExtResource("70_pxdjd")]
transform = Transform3D(1.96047, 0, -0.395691, 0, 2, 0, 0.395691, 0, 1.96047, -13.6883, 2.28551, 15.0586)

[node name="plant_bushLargeTriangle5" parent="Environment/Plants" instance=ExtResource("70_pxdjd")]
transform = Transform3D(0.705905, 0, 1.87128, 0, 2, 0, -1.87128, 0, 0.705905, -13.8169, 2.28551, 14.788)

[node name="plant_bushSmall2" parent="Environment/Plants" instance=ExtResource("71_tpd41")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -13.3611, 2.39939, 15.9217)

[node name="crop_pumpkin2" parent="Environment/Plants" instance=ExtResource("72_copia")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, -11.7526, 2.25359, 16.0375)

[node name="crops_cornStageD2" parent="Environment/Plants" instance=ExtResource("73_fwjgo")]
transform = Transform3D(1.5, 0, 0, 0, 1.5, 0, 0, 0, 1.5, -12.8269, 2.29173, 17.0966)

[node name="crops_cornStageD3" parent="Environment/Plants" instance=ExtResource("73_fwjgo")]
transform = Transform3D(1.4765, 0, -0.264468, 0, 1.5, 0, 0.264468, 0, 1.4765, -12.4615, 2.29173, 17.1516)

[node name="crops_cornStageD4" parent="Environment/Plants" instance=ExtResource("73_fwjgo")]
transform = Transform3D(1.06495, 0, -1.05636, 0, 1.5, 0, 1.05636, 0, 1.06495, -12.0378, 2.29173, 17.3451)

[node name="crops_cornStageD5" parent="Environment/Plants" instance=ExtResource("73_fwjgo")]
transform = Transform3D(0.314274, 0, 1.46671, 0, 1.5, 0, -1.46671, 0, 0.314274, -11.7248, 2.29173, 17.0149)

[node name="Log" type="Node3D" parent="Environment"]

[node name="log_stack2" parent="Environment/Log" instance=ExtResource("74_4hvau")]
transform = Transform3D(2.3555, 0, -1.85786, 0, 3, 0, 1.85786, 0, 2.3555, -24.727, 3.96496, 5.86065)

[node name="log_stackLarge2" parent="Environment/Log" instance=ExtResource("75_dkec4")]
transform = Transform3D(-0.550722, 0, -2.94902, 0, 3, 0, 2.94902, 0, -0.550722, -26.941, 3.92155, 8.0754)

[node name="log2" parent="Environment/Log" instance=ExtResource("76_6cjor")]
transform = Transform3D(-1.6984, 0, -2.47294, 0, 3, 0, 2.47294, 0, -1.6984, 12.1651, 0.147028, -20.0259)

[node name="log3" parent="Environment/Log" instance=ExtResource("76_6cjor")]
transform = Transform3D(-2.55057, 0.332792, 1.54396, -0.0311586, 2.92148, -0.68118, -1.57912, -0.595169, -2.48036, 12.1651, 0.38617, -21.3631)

[node name="log4" parent="Environment/Log" instance=ExtResource("76_6cjor")]
transform = Transform3D(-2.65319, 0.572405, 1.27787, 0.299691, 2.90657, -0.679723, -1.36777, -0.473488, -2.62774, 12.7527, 0.42007, -21.0034)

[node name="log_large2" parent="Environment/Log" instance=ExtResource("77_jmnu3")]
transform = Transform3D(1.58983, 0, -0.180098, 0, 1.6, 0, 0.180098, 0, 1.58983, 12.1215, 0.14601, -18.7216)

[node name="log_stack4" parent="Environment/Log" instance=ExtResource("74_4hvau")]
transform = Transform3D(2, 0, 0, 0, 2, 0, 0, 0, 2, 13.0072, 0.0941358, -16.1477)

[node name="log_stack5" parent="Environment/Log" instance=ExtResource("74_4hvau")]
transform = Transform3D(1.26741, 0, -1.54715, 0, 2, 0, 1.54715, 0, 1.26741, 12.2366, 0.0941358, -17.4392)

[node name="Campfire" type="Node3D" parent="Environment"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 8.66667, 0, -24)

[node name="campfire_stones2" parent="Environment/Campfire" instance=ExtResource("78_h553u")]
transform = Transform3D(4, 0, 0, 0, 4, 0, 0, 0, 4, -2.70173, 0.183827, 5.72278)

[node name="tent_detailedOpen2" parent="Environment/Campfire" instance=ExtResource("79_olm36")]
transform = Transform3D(-6.9282, 0, 4, 0, 8, 0, -4, 0, -6.9282, 0.145481, 0, 0.46048)

[node name="campfire_logs2" parent="Environment/Campfire" instance=ExtResource("80_uxtd5")]
transform = Transform3D(2.49218, 0, -2.45745, 0, 3.5, 0, 2.45745, 0, 2.49218, -2.567, 0.0832412, 5.70366)

[node name="bed_floor2" parent="Environment/Campfire" instance=ExtResource("81_rliao")]
transform = Transform3D(-3.06409, 0, 2.57126, 0, 4, 0, -2.57126, 0, -3.06409, 0.495428, 0.193007, 7.75667)

[node name="log_stack2" parent="Environment/Campfire" instance=ExtResource("74_4hvau")]
transform = Transform3D(2.92462, 0, -0.66827, 0, 3, 0, 0.66827, 0, 2.92462, -5.30678, 0.183034, 2.58181)

[node name="Mountain" type="Node3D" parent="Environment"]

[node name="rock_tallA3" parent="Environment/Mountain" instance=ExtResource("63_t0hgs")]
transform = Transform3D(-13.7455, 1.10206, 14.5175, 0.646753, 39.978, -0.146345, -14.5135, 0.737763, -13.7558, 9.25135, -5.78928, -31.5986)

[node name="rock_tallB4" parent="Environment/Mountain" instance=ExtResource("64_jrafx")]
transform = Transform3D(-3.43928, 0, -21.7295, 0, 25, 0, 21.7295, 0, -3.43928, -12.2791, -0.066582, -35.0559)

[node name="rock_tallC2" parent="Environment/Mountain" instance=ExtResource("82_5r3jb")]
transform = Transform3D(22.9886, 0, -6.89384, 0, 24, 0, 6.89384, 0, 22.9886, -20.979, 1.51317, -29.7776)

[node name="rock_tallD2" parent="Environment/Mountain" instance=ExtResource("83_4sqjt")]
transform = Transform3D(21.0778, 0, -6.87579, 0, 22, 0, 6.30281, 0, 22.994, -5.81119, -0.0495008, 16.0973)

[node name="rock_tallE2" parent="Environment/Mountain" instance=ExtResource("84_allsl")]
transform = Transform3D(19.7254, 0, -16.9384, 0, 26, 0, 16.9384, 0, 19.7254, 12.0506, -0.499025, 15.2544)

[node name="rock_tallF2" parent="Environment/Mountain" instance=ExtResource("85_bagfi")]
transform = Transform3D(18.1078, 0, -8.49158, 0, 30, 0, 8.49158, 0, 18.1078, -21.9718, 2.46183, 18.6261)

[node name="rock_tallG2" parent="Environment/Mountain" instance=ExtResource("86_tnets")]
transform = Transform3D(19.9736, 0, 1.02789, 0, 30, 0, -1.02789, 0, 19.9736, 2.4438, -0.530029, 17.0373)

[node name="rock_tallH2" parent="Environment/Mountain" instance=ExtResource("87_2rbtg")]
transform = Transform3D(23.9736, 0, -0.938642, 0, 25, 0, 1.12637, 0, 19.978, -16.5053, -1.595, 17.5472)

[node name="rock_tallH3" parent="Environment/Mountain" instance=ExtResource("87_2rbtg")]
transform = Transform3D(23.9736, 0, -0.938642, 0, 25, 0, 1.12637, 0, 19.978, -16.5053, -1.595, 17.5472)

[node name="rock_smallE2" parent="Environment/Mountain" instance=ExtResource("88_p2be1")]
transform = Transform3D(6, 0, 0, 0, 12, 0, 0, 0, 6, -13.0391, 21.2138, -37.7938)

[node name="Fence" type="Node3D" parent="Environment"]

[node name="fence_planks2" parent="Environment/Fence" instance=ExtResource("89_7bnen")]
transform = Transform3D(5.79007, 0, -1.31103, 0, 5, 0, 1.57324, 0, 4.82506, -27.1971, 4.01099, 16.001)

[node name="fence_simpleCenter2" parent="Environment/Fence" instance=ExtResource("90_2lmap")]
transform = Transform3D(-1.09278e-07, 0, -2.5, 0, 2.5, 0, 2.5, 0, -1.09278e-07, 12.7968, 0.127605, -22.5738)

[node name="fence_simpleCenter3" parent="Environment/Fence" instance=ExtResource("90_2lmap")]
transform = Transform3D(-1.09278e-07, 0, -2.5, 0, 2.5, 0, 2.5, 0, -1.09278e-07, 12.7968, 0.127605, -20.2232)

[node name="fence_simpleCenter4" parent="Environment/Fence" instance=ExtResource("90_2lmap")]
transform = Transform3D(-1.09278e-07, 0, -2.5, 0, 2.5, 0, 2.5, 0, -1.09278e-07, 12.7968, 0.127605, -17.8899)

[node name="fence_simpleCenter5" parent="Environment/Fence" instance=ExtResource("90_2lmap")]
transform = Transform3D(-1.09278e-07, 0, -2.5, 0, 2.5, 0, 2.5, 0, -1.09278e-07, 12.7968, 0.127605, -15.5566)

[node name="fence_simpleCenter6" parent="Environment/Fence" instance=ExtResource("90_2lmap")]
transform = Transform3D(-1.09278e-07, 0, -2.5, 0, 2.5, 0, 2.5, 0, -1.09278e-07, 12.7968, 0.127605, -13.2232)

[node name="fence_bendCenter2" parent="Environment/Fence" instance=ExtResource("91_2g4cr")]
transform = Transform3D(-1.09278e-07, 0, -2.5, 0, 2.5, 0, 2.5, 0, -1.09278e-07, 12.7968, 0.127605, -10.8899)

[node name="rock_smallI2" parent="Environment/Fence" instance=ExtResource("61_fv6dp")]
transform = Transform3D(1.84171, 0, 1.37772, 0, 3, 0, -1.37772, 0, 1.84171, 13.8075, 0.120593, -24.2681)

[node name="FloorCollision" type="StaticBody3D" parent="."]
transform = Transform3D(1.5, 0, 0, 0, 1.5, 0, 0, 0, 1.5, 0, 0, 0)

[node name="CollisionShape3D" type="CollisionShape3D" parent="FloorCollision"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1.23623, -0.246582, 4.34067)
shape = SubResource("BoxShape3D_l1bbt")

[node name="CollisionShape3D5" type="CollisionShape3D" parent="FloorCollision"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -17.5916, -0.246582, 9.27327)
shape = SubResource("BoxShape3D_2140p")

[node name="CollisionShape3D4" type="CollisionShape3D" parent="FloorCollision"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -17.8851, -0.621319, -7.43035)
shape = SubResource("BoxShape3D_q4s3c")

[node name="CollisionShape3D3" type="CollisionShape3D" parent="FloorCollision"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1.16084, -0.36999, -7.42698)
shape = SubResource("BoxShape3D_3p1xs")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="FloorCollision"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1.10404, -0.246582, -19.1495)
shape = SubResource("BoxShape3D_fmjjh")

[node name="CollisionShape3D6" type="CollisionShape3D" parent="FloorCollision"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -18.4619, -0.246582, -23.7528)
shape = SubResource("BoxShape3D_yhwsg")

[node name="BridgeCollision" type="StaticBody3D" parent="."]
transform = Transform3D(1.5, 0, 0, 0, 1.5, 0, 0, 0, 1.5, -2.15208, 0, 1.87801)

[node name="CollisionShape3D" type="CollisionShape3D" parent="BridgeCollision"]
transform = Transform3D(1, 0, 0, 0, 0.814034, -0.580817, 0, 0.580817, 0.814034, 0.0158234, 0.371233, -2.31149)
shape = SubResource("BoxShape3D_qmho6")

[node name="CollisionShape3D5" type="CollisionShape3D" parent="BridgeCollision"]
transform = Transform3D(-1, -5.07667e-08, -7.11722e-08, 0, 0.814115, -0.580703, 8.74228e-08, -0.580703, -0.814115, 0.0158234, 0.371233, -14.3115)
shape = SubResource("BoxShape3D_qmho6")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="BridgeCollision"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.00604248, 0.71078, -8.39629)
shape = SubResource("BoxShape3D_7q62w")

[node name="CollisionShape3D3" type="CollisionShape3D" parent="BridgeCollision"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1.80057, 1.08095, -8.32636)
shape = SubResource("BoxShape3D_5bci7")

[node name="CollisionShape3D4" type="CollisionShape3D" parent="BridgeCollision"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1.79504, 1.06895, -8.32636)
shape = SubResource("BoxShape3D_5bci7")

[node name="WaterFall" type="MeshInstance3D" parent="."]
transform = Transform3D(-3.89699e-07, 0, 5.2219, 0, 12.3539, 0, -8.91528, 0, -2.28256e-07, -36.7948, -0.525504, -11.0557)
material_override = SubResource("ShaderMaterial_507rw")
mesh = SubResource("ArrayMesh_tecrg")
skeleton = NodePath("")

[node name="GPUParticles3D2" type="GPUParticles3D" parent="."]
transform = Transform3D(3.77489e-07, 0, 5, 0, 5, 0, -5, 0, 3.77489e-07, -32.3253, 0.23643, -10.9194)
amount = 200
fixed_fps = 60
process_material = SubResource("ParticleProcessMaterial_6dksy")
draw_pass_1 = SubResource("SphereMesh_hlix5")

[node name="GPUParticles3D" type="GPUParticles3D" parent="."]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -30.8415, 0.882634, -11.2277)
amount = 100
lifetime = 0.5
fixed_fps = 60
interpolate = false
fract_delta = false
process_material = SubResource("ParticleProcessMaterial_bxal7")
draw_pass_1 = SubResource("QuadMesh_1uiyl")

[node name="WaterFall2" type="MeshInstance3D" parent="."]
transform = Transform3D(-3.88485e-07, 0, 5.2219, 0, 7.98895, 0, -8.88751, 0, -2.28256e-07, 21.6264, -9.82647, -11.1357)
material_override = SubResource("ShaderMaterial_507rw")
mesh = SubResource("ArrayMesh_s4xlb")
skeleton = NodePath("")

[node name="touchInputs" type="CanvasLayer" parent="."]

[node name="global" type="Control" parent="touchInputs"]
layout_mode = 3
anchors_preset = 0
offset_left = 56.0
offset_top = 176.0
offset_right = 152.0
offset_bottom = 264.0
metadata/_edit_use_anchors_ = true

[node name="chat" type="TouchScreenButton" parent="touchInputs/global"]
position = Vector2(10, 10)
scale = Vector2(1.5, 1.5)
texture_normal = ExtResource("96_vurqs")
action = "chat"
metadata/_edit_lock_ = true

[node name="local" type="Control" parent="touchInputs"]
layout_mode = 3
anchors_preset = 0
offset_left = 56.0
offset_top = 88.0
offset_right = 152.0
offset_bottom = 176.0
metadata/_edit_use_anchors_ = true

[node name="chat" type="TouchScreenButton" parent="touchInputs/local"]
position = Vector2(10, 10)
scale = Vector2(1.5, 1.5)
texture_normal = ExtResource("97_qjith")
metadata/_edit_lock_ = true

[node name="Virtual Joystick" parent="touchInputs" instance=ExtResource("95_24n83")]
anchors_preset = -1
offset_left = 64.0
offset_top = -592.0
offset_right = 490.0
offset_bottom = -168.0
grow_horizontal = 2
grow_vertical = 2
scale = Vector2(1.2, 1.2)
joystick_mode = 1
action_left = "move_left"
action_right = "move_right"
action_up = "move_up"
action_down = "move_down"

[node name="jump" type="Control" parent="touchInputs"]
layout_mode = 3
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -317.0
offset_top = -320.0
offset_right = -131.0
offset_bottom = -135.0
grow_horizontal = 0
grow_vertical = 0
scale = Vector2(1.2, 1.2)
script = ExtResource("96_nepnn")
metadata/_edit_use_anchors_ = true

[node name="TouchScreenButton" type="TouchScreenButton" parent="touchInputs/jump"]
position = Vector2(0, 6.66669)
scale = Vector2(0.353282, 0.353282)
texture_normal = ExtResource("97_fjbq0")
action = "jump"

[node name="Ocean" parent="." instance=ExtResource("104_wcd50")]
transform = Transform3D(5, 0, 0, 0, 5, 0, 0, 0, 5, -18.2807, -9.70191, -8.79731)

[node name="MountainCollisions" type="StaticBody3D" parent="."]

[node name="CollisionShape3D" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(0.877205, 0, -0.480116, 0, 1, 0, 0.480116, 0, 0.877205, -20.3729, 1.13637, 20.7629)
shape = SubResource("BoxShape3D_n1eos")

[node name="CollisionShape3D23" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -19.5068, 4.40986, 25.9537)
shape = SubResource("BoxShape3D_qh7xv")

[node name="CollisionShape3D25" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(0.953738, 0, 0.300639, 0, 1, 0, -0.300639, 0, 0.953738, -14.776, 0.191652, 0.747321)
shape = SubResource("BoxShape3D_vbwoa")

[node name="CollisionShape3D27" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(0.99998, 0, -0.00638786, 0, 1, 0, 0.00638786, 0, 0.99998, 19.5654, 0.191652, -24.2162)
shape = SubResource("BoxShape3D_ly2ws")

[node name="CollisionShape3D28" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(0.611734, 0, -0.791063, 0, 1, 0, 0.791063, 0, 0.611734, 18.3688, 0.361586, -26.1913)
shape = SubResource("BoxShape3D_eexbc")

[node name="CollisionShape3D34" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(0.965436, 0, -0.260639, 0, 1, 0, 0.260639, 0, 0.965436, 4.86512, 0.361586, -32.1486)
shape = SubResource("BoxShape3D_q54y4")

[node name="CollisionShape3D33" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(0.763289, 0, -0.646057, 0, 1, 0, 0.646057, 0, 0.763289, 13.6855, 0.0564938, -24.3012)
shape = SubResource("BoxShape3D_4rjhi")

[node name="CollisionShape3D31" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(0.757405, 0, -0.652945, 0, 1, 0, 0.652945, 0, 0.757405, 19.9559, 0.361586, -16.2332)
shape = SubResource("BoxShape3D_iwrab")

[node name="CollisionShape3D32" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(0.450815, 0, -0.892617, 0, 1, 0, 0.892617, 0, 0.450815, 18.7211, 0.361586, -14.9604)
shape = SubResource("BoxShape3D_jfdlo")

[node name="CollisionShape3D29" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(-0.10149, 0, -0.994837, 0, 1, 0, 0.994837, 0, -0.10149, 18.1227, 0.361586, -28.0776)
shape = SubResource("BoxShape3D_q8l68")

[node name="CollisionShape3D30" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(-0.543951, 0, -0.839117, 0, 1, 0, 0.839117, 0, -0.543951, 18.3276, 0.758801, -30.1719)
shape = SubResource("BoxShape3D_jw0hh")

[node name="CollisionShape3D26" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(0.953738, 0, 0.300639, 0, 1, 0, -0.300639, 0, 0.953738, -13.9798, 1.00897, 3.00528)
shape = SubResource("BoxShape3D_x1g7i")

[node name="CollisionShape3D3" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(0.854395, 0, 0.519624, 0, 1, 0, -0.519624, 0, 0.854395, -10.5732, 1.13637, 19.9285)
shape = SubResource("BoxShape3D_u8du3")

[node name="CollisionShape3D10" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(0.791955, -0.18164, 0.582936, -0.00912031, 0.9511, 0.30875, -0.610511, -0.249832, 0.751572, 7.2109, 0.864819, -41.7403)
shape = SubResource("BoxShape3D_2agkh")

[node name="CollisionShape3D11" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(0.973383, 0.0863652, -0.212288, 0, 0.926279, 0.376839, 0.229183, -0.366808, 0.901624, 3.61398, 0.866498, -40.2732)
shape = SubResource("BoxShape3D_l0wde")

[node name="CollisionShape3D12" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(0.208756, 0.365615, -0.907054, 0.0536616, 0.921809, 0.383912, 0.976494, -0.128818, 0.172813, 2.20714, 0.884907, -41.4029)
shape = SubResource("BoxShape3D_ehj8d")

[node name="CollisionShape3D13" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.690733, 1.10208, -42.2915)
shape = SubResource("BoxShape3D_8qk60")

[node name="CollisionShape3D14" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(0.409366, 0, 0.912371, 0, 1, 0, -0.91237, 0, 0.409366, -1.06931, 1.22615, -40.7292)
shape = SubResource("BoxShape3D_123rc")

[node name="CollisionShape3D15" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -4.22733, 1.20192, -39.2252)
shape = SubResource("BoxShape3D_clesg")

[node name="CollisionShape3D16" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(0.425416, 0, -0.904998, 0, 1, 0, 0.904998, 0, 0.425416, -7.8985, 1.18166, -41.8445)
shape = SubResource("BoxShape3D_7qfaj")

[node name="CollisionShape3D17" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(0.931837, -0.082043, 0.353481, 7.45058e-09, 0.974106, 0.22609, -0.362877, -0.210679, 0.907708, -12.0855, 1.28756, -42.6394)
shape = SubResource("BoxShape3D_tpr76")

[node name="CollisionShape3D18" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -22.9092, 1.24272, -41.797)
shape = SubResource("BoxShape3D_txoqd")

[node name="CollisionShape3D19" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(0.544595, 0, 0.838699, 0, 1, 0, -0.838699, 0, 0.544595, -33.4491, 1.2664, -35.0254)
shape = SubResource("BoxShape3D_75flo")

[node name="CollisionShape3D20" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -37.7873, 1.2664, -10.8705)
shape = SubResource("BoxShape3D_1q8q6")

[node name="CollisionShape3D4" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(0.659123, 0, -0.752035, 0, 1, 0, 0.752035, 0, 0.659123, -3.58159, 1.13637, 19.2218)
shape = SubResource("BoxShape3D_wvvbj")

[node name="CollisionShape3D5" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(0.84728, 0, 0.531147, 0, 1, 0, -0.531147, 0, 0.84728, 1.02654, 1.13637, 20.0174)
shape = SubResource("BoxShape3D_olybj")

[node name="CollisionShape3D6" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(0.883627, 0, -0.468192, 0, 1, 0, 0.468192, 0, 0.883627, 5.88998, 1.13637, 19.8857)
shape = SubResource("BoxShape3D_xu012")

[node name="CollisionShape3D7" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(0.625958, 0.145833, 0.7661, 0.0134084, 0.980202, -0.197545, -0.779742, 0.133927, 0.61161, 10.4879, 1.08487, 19.4179)
shape = SubResource("BoxShape3D_23vet")

[node name="CollisionShape3D8" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(0.947599, 0.0902242, 0.306458, -0.0114448, 0.968262, -0.249677, -0.319258, 0.233086, 0.918556, 15.643, 1.03413, 16.4449)
shape = SubResource("BoxShape3D_k4eby")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(0.935351, 0.0494545, 0.350246, 3.72529e-09, 0.990178, -0.139813, -0.35372, 0.130774, 0.926165, -25.8745, 1.63897, 19.0067)
shape = SubResource("BoxShape3D_jfgfn")

[node name="CollisionShape3D21" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(0.874721, -0.484626, 2.11837e-08, 0, -4.37114e-08, -1, 0.484626, 0.874721, -3.82353e-08, -21.3454, 3.26334, 22.4546)
shape = SubResource("BoxShape3D_looav")

[node name="CollisionShape3D22" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(0.874721, -0.484626, 2.11837e-08, 0, -4.37114e-08, -1, 0.484626, 0.874721, -3.82353e-08, -26.8554, 3.26334, 20.4961)
shape = SubResource("BoxShape3D_b0t1j")

[node name="CollisionShape3D24" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(0.874721, -0.484626, 2.11837e-08, 0, -4.37114e-08, -1, 0.484626, 0.874721, -3.82353e-08, -19.9318, 3.40006, 23.9243)
shape = SubResource("BoxShape3D_5s3lq")

[node name="CollisionShape3D9" type="CollisionShape3D" parent="MountainCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 21.4359, 0.72731, -11.5389)
shape = SubResource("BoxShape3D_qw44k")

[node name="TreeCollisions" type="StaticBody3D" parent="."]

[node name="CollisionShape3D" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 15, 1, -2.71896)
shape = SubResource("CylinderShape3D_6p20i")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 16.4, 1, -0.218961)
shape = SubResource("CylinderShape3D_6p20i")

[node name="CollisionShape3D3" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 16.2624, 1, 1.98941)
shape = SubResource("CylinderShape3D_6p20i")

[node name="CollisionShape3D5" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 17.103, 1, 7.44413)
shape = SubResource("CylinderShape3D_6p20i")

[node name="CollisionShape3D7" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 18.9847, 1, 5.83202)
shape = SubResource("CylinderShape3D_6p20i")

[node name="CollisionShape3D10" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 19.2252, 1, 1.00483)
shape = SubResource("CylinderShape3D_6p20i")

[node name="CollisionShape3D11" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 20.0539, 1, -2.26302)
shape = SubResource("CylinderShape3D_6p20i")

[node name="CollisionShape3D12" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 19.942, 1, -5.59106)
shape = SubResource("CylinderShape3D_6p20i")

[node name="CollisionShape3D14" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 17.1126, 1, -7.07101)
shape = SubResource("CylinderShape3D_6p20i")

[node name="CollisionShape3D26" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 19.0265, 0.268336, -12.1515)
shape = SubResource("CylinderShape3D_olp12")

[node name="CollisionShape3D27" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 19.0265, 0.268336, -10.4913)
shape = SubResource("CylinderShape3D_jknre")

[node name="CollisionShape3D15" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -15.4128, 1, 5.72244)
shape = SubResource("CylinderShape3D_6p20i")

[node name="CollisionShape3D23" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -21.3923, 4.33372, 24.8821)
shape = SubResource("CylinderShape3D_6p20i")

[node name="CollisionShape3D24" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -17.5584, 3.81827, 24.1969)
shape = SubResource("CylinderShape3D_atdhh")

[node name="CollisionShape3D16" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -16.9614, 1, 3.06933)
shape = SubResource("CylinderShape3D_6p20i")

[node name="CollisionShape3D17" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -18.4504, 1, 5.96025)
shape = SubResource("CylinderShape3D_6p20i")

[node name="CollisionShape3D18" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -18.4504, 1, -37.8568)
shape = SubResource("CylinderShape3D_6p20i")

[node name="CollisionShape3D25" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 8.94841, 0.239957, -27.4818)
shape = SubResource("CylinderShape3D_5aaxs")

[node name="CollisionShape3D20" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -29.2472, 1, -35.9478)
shape = SubResource("CylinderShape3D_6p20i")

[node name="CollisionShape3D19" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -23.5032, 1, -34.7027)
shape = SubResource("CylinderShape3D_mcwbp")

[node name="CollisionShape3D21" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -33.8705, 1, -19.5368)
shape = SubResource("CylinderShape3D_uy6kp")

[node name="CollisionShape3D22" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -35.0076, 1, -2.68847)
shape = SubResource("CylinderShape3D_uy6kp")

[node name="CollisionShape3D13" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 16.0309, 1, -4.94945)
shape = SubResource("CylinderShape3D_6jwyo")

[node name="CollisionShape3D9" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 18.9847, 1, 3.33202)
shape = SubResource("CylinderShape3D_siuev")

[node name="CollisionShape3D6" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 15.9781, 1, 9.17318)
shape = SubResource("CylinderShape3D_6p20i")

[node name="CollisionShape3D4" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 16.9172, 1, 4.45715)
shape = SubResource("CylinderShape3D_8ks7p")

[node name="CollisionShape3D8" type="CollisionShape3D" parent="TreeCollisions"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 19.342, 1, 9.6221)
shape = SubResource("CylinderShape3D_hyg4w")

[node name="GlobalChat" type="CanvasLayer" parent="."]

[node name="GlobalChatPanel" type="Panel" parent="GlobalChat"]
unique_name_in_owner = true
anchors_preset = -1
anchor_left = 0.562
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 8.95996
offset_right = 8.95996
grow_horizontal = 0
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_qjith")

[node name="Label" type="Label" parent="GlobalChat/GlobalChatPanel"]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -177.5
offset_top = 32.0
offset_right = 177.5
offset_bottom = 88.0
grow_horizontal = 2
theme_override_fonts/font = ExtResource("102_5lshx")
theme_override_font_sizes/font_size = 48
text = "Global Chat Panel"

[node name="GlobalTextField" type="LineEdit" parent="GlobalChat/GlobalChatPanel"]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -352.0
offset_top = -96.0
offset_right = 296.0
offset_bottom = -49.0
grow_horizontal = 2
grow_vertical = 0

[node name="SendGlobal_message" type="Button" parent="GlobalChat/GlobalChatPanel"]
unique_name_in_owner = true
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -112.0
offset_top = -96.0
offset_right = -64.0
offset_bottom = -48.0
grow_horizontal = 0
grow_vertical = 0
theme_override_styles/focus = SubResource("StyleBoxEmpty_74gn8")
theme_override_styles/hover = SubResource("StyleBoxEmpty_8hidn")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_wan5x")
theme_override_styles/normal = SubResource("StyleBoxEmpty_ao4ib")
icon = ExtResource("103_vurqs")

[node name="ScrollContainer" type="ScrollContainer" parent="GlobalChat/GlobalChatPanel"]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.077
anchor_top = 0.12
anchor_right = 0.923
anchor_bottom = 0.88
offset_left = -0.064003
offset_top = 0.319992
offset_right = 0.0639648
offset_bottom = -0.320068
grow_horizontal = 2
grow_vertical = 2

[node name="MessageList" type="VBoxContainer" parent="GlobalChat/GlobalChatPanel/ScrollContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_constants/separation = 8

[node name="Cloud" type="MeshInstance3D" parent="."]
transform = Transform3D(50, 0, 0, 0, 50, 0, 0, 0, 50, 0, -26.6298, 0)
material_override = SubResource("ShaderMaterial_sn4s7")
cast_shadow = 0
mesh = SubResource("SphereMesh_qkeux")
skeleton = NodePath("../..")

[connection signal="pressed" from="touchInputs/global/chat" to="." method="_on_global_chat_pressed"]
[connection signal="pressed" from="touchInputs/local/chat" to="." method="_on_local_chat_pressed"]
[connection signal="pressed" from="touchInputs/jump/TouchScreenButton" to="touchInputs/jump" method="_on_touch_screen_button_pressed"]
[connection signal="released" from="touchInputs/jump/TouchScreenButton" to="touchInputs/jump" method="_on_touch_screen_button_released"]
[connection signal="pressed" from="GlobalChat/GlobalChatPanel/SendGlobal_message" to="." method="_on_send_global_message_pressed"]
